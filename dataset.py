import os
import torch
import numpy as np
import pandas as pd
from torch.utils.data import Dataset

class Efficient3DDataset(Dataset):
    def __init__(self, csv_file, root_dir, transform=None):
        self.labels_frame = pd.read_csv(csv_file)
        self.root_dir = root_dir
        self.transform = transform

    def __len__(self):
        return len(self.labels_frame)

    def __getitem__(self, idx):
        filename = self.labels_frame.iloc[idx, 0]
        label = self.labels_frame.iloc[idx, 1]
        
        filepath = os.path.join(self.root_dir, filename)
        x_numpy = np.load(filepath)
        
        x_tensor = torch.from_numpy(x_numpy).float()
        y_tensor = torch.tensor(label, dtype=torch.float)

        if self.transform:
            x_tensor = self.transform(x_tensor)
            
        return x_tensor, y_tensor