{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0545cecb", "metadata": {}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from torch.nn import functional as F\n", "from torch.utils import data\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "\n", "import os\n", "import time\n", "import matplotlib.pyplot as plt\n", "\n", "#from network-blocks import SeparableConv3d, ResidualLayer, create_stacked_blocks\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "99597c40", "metadata": {}, "outputs": [{"data": {"text/plain": ["'2.7.0'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.__version__"]}, {"cell_type": "code", "execution_count": 3, "id": "2dddc04f", "metadata": {}, "outputs": [], "source": ["\n", "class SeparableConv3d(nn.Module):\n", "    \"\"\"\n", "    一个完整的3D 深度可分离卷积模块\n", "    包含: 深度卷积 -> [BN -> ReLU] -> 逐点卷积 \n", "    \"\"\"\n", "    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=False, middle_bn_relu=False):\n", "        \"\"\"\n", "        :param in_channels: 输入通道数\n", "        :param out_channels: 输出通道数\n", "        :param kernel_size: 深度卷积的核大小\n", "        :param stride: 深度卷积的步长\n", "        :param padding: 深度卷积的填充\n", "        \"\"\"\n", "        super(SeparableConv3d, self).__init__()\n", "        \n", "\n", "            # --- 1. 深度卷积 (Depthwise Convolution) ---\n", "            # groups=in_channels 确保了每个输入通道都有自己独立的卷积核\n", "            # 输出通道数必须等于输入通道数\n", "            # 通常不在深度卷积后加偏置，因为BN层会进行中心化处理\n", "        if middle_bn_relu:\n", "            self.depthwise_conv = nn.Sequential(\n", "                nn.Conv3d(in_channels, in_channels, kernel_size, stride, padding, groups=in_channels, bias=bias),\n", "                nn.BatchNorm3d(in_channels),\n", "                nn.ReLU())\n", "        else:\n", "            self.depthwise_conv = nn.Sequential(\n", "                nn.Conv3d(in_channels, in_channels, kernel_size, stride, padding, groups=in_channels, bias=bias))     \n", "            \n", "            # --- 2. 逐点卷积 (Pointwise Convolution) ---\n", "            # 1x1的标准卷积，用于组合通道特征\n", "            # 同样，通常不加偏置\n", "        \n", "        self.pointwise_conv = nn.Sequential(\n", "            nn.Conv3d(in_channels, out_channels, kernel_size=1, stride=1, padding=0, bias=bias))\n", "        \n", "\n", "    def forward(self, x):\n", "        x = self.depthwise_conv(x)\n", "        x = self.pointwise_conv(x)\n", "        return x"]}, {"cell_type": "code", "execution_count": 4, "id": "c4a983ed", "metadata": {}, "outputs": [], "source": ["# b1 = nn.Sequential(nn.Conv3d(16, 64, kernel_size=5, stride=2, padding=2),\n", "#                    nn.<PERSON><PERSON><PERSON><PERSON>3<PERSON>(64), nn.<PERSON><PERSON><PERSON>())"]}, {"cell_type": "code", "execution_count": 5, "id": "a22373e5", "metadata": {}, "outputs": [], "source": ["b1 = nn.Sequential(SeparableConv3d(13, 64, kernel_size=5, stride=2, padding=2))  # first parameter is input_channels \n", "                    #nn.<PERSON><PERSON><PERSON><PERSON>3<PERSON>(64), nn.<PERSON><PERSON><PERSON>())"]}, {"cell_type": "code", "execution_count": 6, "id": "a83fd799", "metadata": {}, "outputs": [], "source": ["class ResidualLayer(nn.Module):  \n", "    def __init__(self, input_channels, output_channels,\n", "                 kernel_size=3, strides=1, padding=1, cardinality=16):\n", "        super().__init__()\n", "\n", "        self.cardinality = cardinality\n", "\n", "\n", "        self.BN_ReLU_Conv3D_in = nn.Sequential(nn.BatchNorm3d(input_channels), \\\n", "                                               nn.<PERSON><PERSON><PERSON>(), \\\n", "                                               nn.Conv3d(input_channels, input_channels, kernel_size=1, stride=1, \n", "                                               bias=False))\n", "\n", "        self.BN_ReLU_Conv3D_out = nn.Sequential(nn.BatchNorm3d(input_channels), \\\n", "                                                nn.<PERSON><PERSON><PERSON>(), \\\n", "                                                nn.Conv3d(input_channels, output_channels, kernel_size=1, stride=1, \n", "                                                bias=False))\n", "\n", "        self.sub_channels = input_channels // cardinality \n", "\n", "        self.BN_ReLU = nn.Sequential(nn.BatchNorm3d(input_channels), \\\n", "                                     nn.ReLU()) # after BN_ReLU_Conv3D_in\n", "\n", "        #self.conv1 = nn.Conv3d(input_channels, self.sub_channels, kernel_size=3, stride=1, padding=1) \n", "        self.parallel_convs = nn.ModuleList()\n", "        for _ in range(self.cardinality):\n", "            self.parallel_convs.append(\n", "                SeparableConv3d(\n", "                    in_channels=input_channels,\n", "                    out_channels=self.sub_channels,\n", "                    kernel_size=kernel_size,\n", "                    stride=strides,\n", "                    padding=padding,\n", "                    bias=False\n", "                )\n", "            )\n", "            # self.parallel_convs.append(\n", "            #     nn.Conv3d(\n", "            #         in_channels=input_channels,\n", "            #         out_channels=self.sub_channels,\n", "            #         kernel_size=kernel_size,\n", "            #         stride=strides,\n", "            #         padding=padding\n", "            #     )\n", "            # )\n", "\n", "\n", "        \n", "    def forward(self, X):\n", "\n", "        Y1 = self.BN_ReLU_Conv3D_in(X)\n", "        Y1 = self.BN_ReLU(Y1)\n", "\n", "        branch_outputs = []\n", "        for conv_layer in self.parallel_convs:\n", "            branch_outputs.append(conv_layer(Y1))\n", "        Y1 = torch.cat(branch_outputs, dim=1)\n", "\n", "        Y1 = self.BN_ReLU_Conv3D_out(Y1)\n", "\n", "        Y2 = self.BN_ReLU_Conv3D_out(X) # shortcut branch \n", "        Y1 += Y2\n", "\n", "        return <PERSON><PERSON>re<PERSON>(Y1)"]}, {"cell_type": "code", "execution_count": 7, "id": "c93a4c9b", "metadata": {}, "outputs": [], "source": ["def create_stacked_blocks(num_blocks):\n", "    # 创建一个包含 num_blocks 个 BasicBlock 的 Python 列表\n", "    # 注意：这里我们假设每个 block 的输入输出通道数相同\n", "    blocks = [ResidualLayer(input_channels=64, output_channels=64, cardinality=16) for _ in range(num_blocks)]\n", "    \n", "    # 使用 * 将列表解包，传入 nn.Sequential\n", "    return nn.Sequential(*blocks)"]}, {"cell_type": "code", "execution_count": 8, "id": "fceeec6b", "metadata": {}, "outputs": [], "source": ["stacked_RLx10 = create_stacked_blocks(10) # 10 "]}, {"cell_type": "code", "execution_count": null, "id": "5cc959cc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 9, "id": "19c1bd9e", "metadata": {}, "outputs": [], "source": ["net = nn.Sequential(b1, \\\n", "                    stacked_RLx10, \\\n", "                    nn.MaxPool3d(kernel_size=3, stride=2, padding=0), \\\n", "                    ResidualLayer(input_channels=64, output_channels=256), \\\n", "                    ResidualLayer(input_channels=256, output_channels=256), \\\n", "                    ResidualLayer(input_channels=256, output_channels=256), \\\n", "                    ResidualLayer(input_channels=256, output_channels=256), \\\n", "                    nn.MaxPool3d(kernel_size=3, stride=2, padding=0), \\\n", "                    ResidualLayer(input_channels=256, output_channels=512), \\\n", "                    nn.Dropout(0.2), \\\n", "                    nn.AdaptiveAvgPool3d(1), \\\n", "                    nn.<PERSON>(), \\\n", "                    nn.<PERSON><PERSON>(512, 1)\n", "                    )\n", "\n", "# ResidualLayer(input_channels=64, output_channels=64, cardinality=16))"]}, {"cell_type": "code", "execution_count": 10, "id": "c4fcdcd5", "metadata": {}, "outputs": [], "source": ["X = torch.rand(size=(1, 13, 30, 30, 30))"]}, {"cell_type": "code", "execution_count": 11, "id": "dd35270a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sequential output shape:\t torch.Size([1, 64, 15, 15, 15])\n", "Sequential output shape:\t torch.Size([1, 64, 15, 15, 15])\n", "MaxPool3d output shape:\t torch.Size([1, 64, 7, 7, 7])\n", "ResidualLayer output shape:\t torch.Size([1, 256, 7, 7, 7])\n", "ResidualLayer output shape:\t torch.Size([1, 256, 7, 7, 7])\n", "ResidualLayer output shape:\t torch.Size([1, 256, 7, 7, 7])\n", "ResidualLayer output shape:\t torch.Size([1, 256, 7, 7, 7])\n", "MaxPool3d output shape:\t torch.Size([1, 256, 3, 3, 3])\n", "ResidualLayer output shape:\t torch.Size([1, 512, 3, 3, 3])\n", "Dropout output shape:\t torch.Size([1, 512, 3, 3, 3])\n", "AdaptiveAvgPool3d output shape:\t torch.Size([1, 512, 1, 1, 1])\n", "Flatten output shape:\t torch.Size([1, 512])\n", "Linear output shape:\t torch.Size([1, 1])\n"]}], "source": ["for layer in net:\n", "    X = layer(X)\n", "    print(layer.__class__.__name__,'output shape:\\t', X.shape)"]}, {"cell_type": "code", "execution_count": 12, "id": "7987822c", "metadata": {}, "outputs": [{"data": {"text/plain": ["===============================================================================================\n", "Layer (type:depth-idx)                        Output Shape              Param #\n", "===============================================================================================\n", "Sequential                                    [1, 1]                    --\n", "├─Sequential: 1-1                             [1, 64, 15, 15, 15]       --\n", "│    └─SeparableConv3d: 2-1                   [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-1                   [1, 13, 15, 15, 15]       1,625\n", "│    │    └─Sequential: 3-2                   [1, 64, 15, 15, 15]       832\n", "├─Sequential: 1-2                             [1, 64, 15, 15, 15]       --\n", "│    └─ResidualLayer: 2-2                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-3                   [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-4                   [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-5                   --                        31,744\n", "│    │    └─Sequential: 3-6                   [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-7                   [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-3                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-8                   [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-9                   [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-10                  --                        31,744\n", "│    │    └─Sequential: 3-11                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-12                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-4                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-13                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-14                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-15                  --                        31,744\n", "│    │    └─Sequential: 3-16                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-17                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-5                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-18                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-19                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-20                  --                        31,744\n", "│    │    └─Sequential: 3-21                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-22                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-6                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-23                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-24                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-25                  --                        31,744\n", "│    │    └─Sequential: 3-26                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-27                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-7                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-28                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-29                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-30                  --                        31,744\n", "│    │    └─Sequential: 3-31                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-32                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-8                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-33                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-34                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-35                  --                        31,744\n", "│    │    └─Sequential: 3-36                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-37                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-9                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-38                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-39                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-40                  --                        31,744\n", "│    │    └─Sequential: 3-41                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-42                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-10                    [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-43                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-44                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-45                  --                        31,744\n", "│    │    └─Sequential: 3-46                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-47                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-11                    [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-48                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-49                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-50                  --                        31,744\n", "│    │    └─Sequential: 3-51                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-52                  [1, 64, 15, 15, 15]       (recursive)\n", "├─MaxPool3d: 1-3                              [1, 64, 7, 7, 7]          --\n", "├─ResidualLayer: 1-4                          [1, 256, 7, 7, 7]         --\n", "│    └─Sequential: 2-12                       [1, 64, 7, 7, 7]          --\n", "│    │    └─BatchNorm3d: 3-53                 [1, 64, 7, 7, 7]          128\n", "│    │    └─ReLU: 3-54                        [1, 64, 7, 7, 7]          --\n", "│    │    └─Conv3d: 3-55                      [1, 64, 7, 7, 7]          4,096\n", "│    └─Sequential: 2-13                       [1, 64, 7, 7, 7]          --\n", "│    │    └─BatchNorm3d: 3-56                 [1, 64, 7, 7, 7]          128\n", "│    │    └─ReLU: 3-57                        [1, 64, 7, 7, 7]          --\n", "│    └─ModuleList: 2-14                       --                        --\n", "│    │    └─SeparableConv3d: 3-58             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-59             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-60             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-61             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-62             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-63             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-64             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-65             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-66             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-67             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-68             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-69             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-70             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-71             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-72             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-73             [1, 4, 7, 7, 7]           1,984\n", "│    └─Sequential: 2-15                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-74                 [1, 64, 7, 7, 7]          128\n", "│    │    └─ReLU: 3-75                        [1, 64, 7, 7, 7]          --\n", "│    │    └─Conv3d: 3-76                      [1, 256, 7, 7, 7]         16,384\n", "│    └─Sequential: 2-16                       [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─BatchNorm3d: 3-77                 [1, 64, 7, 7, 7]          (recursive)\n", "│    │    └─ReLU: 3-78                        [1, 64, 7, 7, 7]          --\n", "│    │    └─Conv3d: 3-79                      [1, 256, 7, 7, 7]         (recursive)\n", "├─ResidualLayer: 1-5                          [1, 256, 7, 7, 7]         --\n", "│    └─Sequential: 2-17                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-80                 [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-81                        [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-82                      [1, 256, 7, 7, 7]         65,536\n", "│    └─Sequential: 2-18                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-83                 [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-84                        [1, 256, 7, 7, 7]         --\n", "│    └─ModuleList: 2-19                       --                        --\n", "│    │    └─SeparableConv3d: 3-85             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-86             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-87             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-88             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-89             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-90             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-91             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-92             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-93             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-94             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-95             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-96             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-97             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-98             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-99             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-100            [1, 16, 7, 7, 7]          11,008\n", "│    └─Sequential: 2-20                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-101                [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-102                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-103                     [1, 256, 7, 7, 7]         65,536\n", "│    └─Sequential: 2-21                       [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─BatchNorm3d: 3-104                [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─ReLU: 3-105                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-106                     [1, 256, 7, 7, 7]         (recursive)\n", "├─ResidualLayer: 1-6                          [1, 256, 7, 7, 7]         --\n", "│    └─Sequential: 2-22                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-107                [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-108                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-109                     [1, 256, 7, 7, 7]         65,536\n", "│    └─Sequential: 2-23                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-110                [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-111                       [1, 256, 7, 7, 7]         --\n", "│    └─ModuleList: 2-24                       --                        --\n", "│    │    └─SeparableConv3d: 3-112            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-113            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-114            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-115            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-116            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-117            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-118            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-119            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-120            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-121            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-122            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-123            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-124            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-125            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-126            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-127            [1, 16, 7, 7, 7]          11,008\n", "│    └─Sequential: 2-25                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-128                [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-129                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-130                     [1, 256, 7, 7, 7]         65,536\n", "│    └─Sequential: 2-26                       [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─BatchNorm3d: 3-131                [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─ReLU: 3-132                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-133                     [1, 256, 7, 7, 7]         (recursive)\n", "├─ResidualLayer: 1-7                          [1, 256, 7, 7, 7]         --\n", "│    └─Sequential: 2-27                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-134                [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-135                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-136                     [1, 256, 7, 7, 7]         65,536\n", "│    └─Sequential: 2-28                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-137                [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-138                       [1, 256, 7, 7, 7]         --\n", "│    └─ModuleList: 2-29                       --                        --\n", "│    │    └─SeparableConv3d: 3-139            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-140            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-141            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-142            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-143            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-144            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-145            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-146            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-147            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-148            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-149            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-150            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-151            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-152            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-153            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-154            [1, 16, 7, 7, 7]          11,008\n", "│    └─Sequential: 2-30                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-155                [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-156                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-157                     [1, 256, 7, 7, 7]         65,536\n", "│    └─Sequential: 2-31                       [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─BatchNorm3d: 3-158                [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─ReLU: 3-159                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-160                     [1, 256, 7, 7, 7]         (recursive)\n", "├─MaxPool3d: 1-8                              [1, 256, 3, 3, 3]         --\n", "├─ResidualLayer: 1-9                          [1, 512, 3, 3, 3]         --\n", "│    └─Sequential: 2-32                       [1, 256, 3, 3, 3]         --\n", "│    │    └─BatchNorm3d: 3-161                [1, 256, 3, 3, 3]         512\n", "│    │    └─ReLU: 3-162                       [1, 256, 3, 3, 3]         --\n", "│    │    └─Conv3d: 3-163                     [1, 256, 3, 3, 3]         65,536\n", "│    └─Sequential: 2-33                       [1, 256, 3, 3, 3]         --\n", "│    │    └─BatchNorm3d: 3-164                [1, 256, 3, 3, 3]         512\n", "│    │    └─ReLU: 3-165                       [1, 256, 3, 3, 3]         --\n", "│    └─ModuleList: 2-34                       --                        --\n", "│    │    └─SeparableConv3d: 3-166            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-167            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-168            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-169            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-170            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-171            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-172            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-173            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-174            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-175            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-176            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-177            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-178            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-179            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-180            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-181            [1, 16, 3, 3, 3]          11,008\n", "│    └─Sequential: 2-35                       [1, 512, 3, 3, 3]         --\n", "│    │    └─BatchNorm3d: 3-182                [1, 256, 3, 3, 3]         512\n", "│    │    └─ReLU: 3-183                       [1, 256, 3, 3, 3]         --\n", "│    │    └─Conv3d: 3-184                     [1, 512, 3, 3, 3]         131,072\n", "│    └─Sequential: 2-36                       [1, 512, 3, 3, 3]         (recursive)\n", "│    │    └─BatchNorm3d: 3-185                [1, 256, 3, 3, 3]         (recursive)\n", "│    │    └─ReLU: 3-186                       [1, 256, 3, 3, 3]         --\n", "│    │    └─Conv3d: 3-187                     [1, 512, 3, 3, 3]         (recursive)\n", "├─Dropout: 1-10                               [1, 512, 3, 3, 3]         --\n", "├─AdaptiveAvgPool3d: 1-11                     [1, 512, 1, 1, 1]         --\n", "├─Flatten: 1-12                               [1, 512]                  --\n", "├─Linear: 1-13                                [1, 1]                    513\n", "===============================================================================================\n", "Total params: 1,759,258\n", "Trainable params: 1,759,258\n", "Non-trainable params: 0\n", "Total mult-adds (G): 1.92\n", "===============================================================================================\n", "Input size (MB): 1.40\n", "Forward/backward pass size (MB): 474.08\n", "Params size (MB): 7.04\n", "Estimated Total Size (MB): 482.52\n", "==============================================================================================="]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from torchinfo import summary\n", "summary(net, input_size=(1, 13, 30, 30, 30))  # 输入张量的形状"]}, {"cell_type": "code", "execution_count": 13, "id": "559571e5", "metadata": {}, "outputs": [], "source": ["\n", "# class Efficient3DDataset(data.Dataset):\n", "#     def __init__(self, csv_file, root_dir, transform=None):\n", "#         \"\"\"\n", "#         Args:\n", "#             csv_file (string): 包含文件名和标签的CSV文件路径。\n", "#             root_dir (string): 存放所有处理后 .npy 文件的目录。\n", "#             transform (callable, optional): 应用于样本的转换操作。\n", "#         \"\"\"\n", "#         self.labels_frame = pd.read_csv(csv_file)\n", "#         self.root_dir = root_dir\n", "#         self.transform = transform\n", "\n", "#     def __len__(self):\n", "#         return len(self.labels_frame)\n", "\n", "#     def __getitem__(self, idx):\n", "#         # 1. 从DataFrame获取文件名和标签\n", "#         filename = self.labels_frame.iloc[idx, 0]\n", "#         label = self.labels_frame.iloc[idx, 1]\n", "        \n", "#         # 2. 读取单个.npy文件（一次IO操作，非常快）\n", "#         filepath = os.path.join(self.root_dir, filename)\n", "#         x_numpy = np.load(filepath) # 直接得到 (13, 30, 30, 30) 的数组\n", "        \n", "#         # 3. 转换为Tensor\n", "#         x_tensor = torch.from_numpy(x_numpy).float()\n", "#         y_tensor = torch.tensor(label, dtype=torch.float)\n", "\n", "#         if self.transform:\n", "#             x_tensor = self.transform(x_tensor)\n", "            \n", "#         return x_tensor, y_tensor"]}, {"cell_type": "code", "execution_count": 14, "id": "a82c8977", "metadata": {}, "outputs": [], "source": ["from dataset import Efficient3DDataset"]}, {"cell_type": "code", "execution_count": 15, "id": "a11539b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training dataset created successfully. Total samples: 288\n"]}], "source": ["#train_dataset = Efficient3DDataset(csv_file='./to-test/labels.csv', root_dir='./to-test/')\n", "#train_dataset = Efficient3DDataset(csv_file=os.path.expanduser('~/research/other-repos/arontier-PDBbind-2020/core-set_junsu-pdbqt/labels.csv'), root_dir=os.path.expanduser('~/research/other-repos/arontier-PDBbind-2020/core-set_junsu-pdbqt/'))\n", "train_dataset = Efficient3DDataset(csv_file=os.path.expanduser('~/research/other-repos/arontier-PDBbind-2020/mini-core-set_junsu-pdbqt/labels.csv'), root_dir=os.path.expanduser('~/research/other-repos/arontier-PDBbind-2020/mini-core-set_junsu-pdbqt/'))\n", "print(f\"Training dataset created successfully. Total samples: {len(train_dataset)}\")\n"]}, {"cell_type": "code", "execution_count": 16, "id": "180fcf80", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Test dataset created successfully. Total samples: 24\n"]}], "source": ["test_dataset = None\n", "\n", "test_dataset = Efficient3DDataset(csv_file=os.path.expanduser('~/research/other-repos/arontier-PDBbind-2020/mini-core-valid/labels.csv'), root_dir=os.path.expanduser('~/research/other-repos/arontier-PDBbind-2020/mini-core-valid/'))\n", "print(f\"Test dataset created successfully. Total samples: {len(test_dataset)}\")"]}, {"cell_type": "code", "execution_count": 17, "id": "fd5215bc", "metadata": {}, "outputs": [], "source": ["NUM_EPOCHS = 5          # 计划训练的总轮数\n", "BATCH_SIZE = 64          # 批处理大小\n", "LEARNING_RATE = 0.0005   # 学习率\n", "\n", "# 文件与路径设置\n", "SAVE_EVERY_Q_EPOCHS = 10 # 每Q个epoch保存一次checkpoint\n", "MODEL_SAVE_DIR = \"./saved_models\" # checkpoint保存目录\n", "LOG_FILE_PATH = \"./logs/training_log_regression.txt\" # 日志文件路径\n", "NUM_BATCH_WORKERS = 4 \n", "\n", "RESUME_CHECKPOINT_PATH = \"./saved_models/checkpoint_epoch_4.pth\" "]}, {"cell_type": "code", "execution_count": 18, "id": "c18dfe8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cpu\n"]}], "source": ["device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")\n", "\n", "net = net.to(device)"]}, {"cell_type": "code", "execution_count": 19, "id": "e575b82e", "metadata": {}, "outputs": [], "source": ["train_iter = data.DataLoader(dataset=train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=NUM_BATCH_WORKERS) "]}, {"cell_type": "code", "execution_count": 20, "id": "0f685bbd", "metadata": {}, "outputs": [], "source": ["test_iter = None\n", "if test_dataset:\n", "    test_iter = data.DataLoader(dataset=test_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=NUM_BATCH_WORKERS)\n"]}, {"cell_type": "code", "execution_count": 21, "id": "e0ce1bdd", "metadata": {}, "outputs": [], "source": ["loss = nn.<PERSON><PERSON><PERSON>() "]}, {"cell_type": "code", "execution_count": 22, "id": "1c3c1131", "metadata": {}, "outputs": [], "source": ["trainer = torch.optim.Adam(net.parameters(), lr=LEARNING_RATE, betas=(0.99, 0.999))"]}, {"cell_type": "code", "execution_count": 23, "id": "6659241e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Resuming training from checkpoint: ./saved_models/checkpoint_epoch_4.pth\n", "Successfully resumed. Training will continue from epoch 5.\n"]}], "source": ["# ==============================================================================\n", "# 2. 加载 Checkpoint 或 初始化 (Load Checkpoint or Initialize)\n", "# ==============================================================================\n", "start_epoch = 0\n", "history = { 'train_loss': [], 'test_loss': [], 'train_mae': [], 'train_rmse': [], 'test_mae': [], 'test_rmse': [] }\n", "\n", "# 如果指定了checkpoint路径且文件存在，则加载\n", "if RESUME_CHECKPOINT_PATH and os.path.exists(RESUME_CHECKPOINT_PATH):\n", "    print(f\"Resuming training from checkpoint: {RESUME_CHECKPOINT_PATH}\")\n", "    # map_location确保模型能被正确加载到当前设备(CPU/GPU)\n", "    checkpoint = torch.load(RESUME_CHECKPOINT_PATH, map_location=device)\n", "    \n", "    net.load_state_dict(checkpoint['model_state_dict'])\n", "    trainer.load_state_dict(checkpoint['optimizer_state_dict'])\n", "    start_epoch = checkpoint['epoch']\n", "    history = checkpoint['history']\n", "    \n", "    print(f\"Successfully resumed. Training will continue from epoch {start_epoch + 1}.\")\n", "else:\n", "    print(\"No checkpoint found or specified. Starting training from scratch.\")\n", "    # 如果从头开始，则创建新的日志文件\n", "    os.makedirs(os.path.dirname(LOG_FILE_PATH), exist_ok=True)\n", "    with open(LOG_FILE_PATH, 'w') as f:\n", "        header = \"Epoch | Time per Epoch | Train Loss | Test Loss  | Train MAE | Train RMSE  | Test MAE | Test RMSE\\n\"\n", "        f.write(header)\n", "        print(header.strip())\n"]}, {"cell_type": "code", "execution_count": null, "id": "4da1acf5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["== Training: Epoch 5/5 ==\n", "train_total_count:  64\n"]}], "source": ["# ==============================================================================\n", "# 3. 核心训练循环 (Core Training Loop)\n", "# ==============================================================================\n", "# 循环从 start_epoch 开始，到 NUM_EPOCHS 结束\n", "for epoch in range(start_epoch, NUM_EPOCHS):\n", "    print(f\"== Training: Epoch {epoch + 1}/{NUM_EPOCHS} ==\")\n", "    epoch_start_time = time.time()\n", "    \n", "    # --- 训练阶段 ---\n", "    net.train() # 设置为训练模式\n", "    train_loss_sum, train_mae_sum, train_se_sum, train_total_count = 0.0, 0.0, 0.0, 0\n", "    for X, y in train_iter:\n", "        # 将数据移动到指定设备\n", "        X, y = X.to(device), y.to(device)\n", "        \n", "        # 前向传播\n", "        y_hat = net(X)\n", "        \n", "        # 计算损失\n", "        y_hat = y_hat.squeeze() # 移除维度为1的维度，将[batch_size, 1]变为[batch_size]\n", "        l = loss(y_hat, y)\n", "        \n", "        # 反向传播和优化\n", "        trainer.zero_grad() # 梯度清零\n", "        l.backward()        # 计算梯度\n", "        trainer.step()      # 更新权重\n", "        \n", "        # 累加指标\n", "        train_loss_sum += l.item() * X.shape[0] # 当前批次的损失值 * 批次大小\n", "        train_mae_sum += torch.abs(y_hat - y).sum().item()\n", "        train_se_sum += ((y_hat - y) ** 2).sum().item()\n", "        train_total_count += y.shape[0]\n", "        print('train_total_count: ', train_total_count)\n", "\n", "    # 计算当前epoch的平均指标\n", "    avg_train_loss = train_loss_sum / train_total_count\n", "    avg_train_mae = train_mae_sum / train_total_count\n", "    #avg_train_rmse = np.sqrt((train_se_sum / train_total_count))\n", "    avg_train_rmse = torch.sqrt(torch.tensor(train_se_sum / train_total_count))\n", "    history['train_loss'].append(avg_train_loss)\n", "    history['train_mae'].append(avg_train_mae)\n", "    history['train_rmse'].append(avg_train_rmse.item())\n", "\n", "    # --- 测试阶段 (如果提供了test_iter) ---\n", "    avg_test_loss, avg_test_mae = float('nan'), float('nan') # 默认为NaN\n", "    if test_iter:\n", "        net.eval() # 设置为评估模式\n", "        test_loss_sum, test_mae_sum, test_se_sum, test_total_count = 0.0, 0.0, 0.0, 0\n", "        with torch.no_grad(): # 在评估时，不计算梯度\n", "            for X, y in test_iter:\n", "                X, y = X.to(device), y.to(device)\n", "                y_hat = net(X)\n", "                y_hat = y_hat.squeeze() # 移除维度为1的维度，将[batch_size, 1]变为[batch_size]\n", "                l_test = loss(y_hat, y)\n", "                test_loss_sum += l_test.item() * X.shape[0]\n", "                test_mae_sum += torch.abs(y_hat - y).sum().item()\n", "                test_se_sum += ((y_hat - y) ** 2).sum().item()\n", "                test_total_count += y.shape[0]\n", "        \n", "        avg_test_loss = test_loss_sum / test_total_count\n", "        avg_test_mae = test_mae_sum / test_total_count\n", "        avg_test_rmse = torch.sqrt(torch.tensor(test_se_sum / test_total_count))\n", "        \n", "        history['test_loss'].append(avg_test_loss)\n", "        history['test_mae'].append(avg_test_mae)\n", "        history['test_rmse'].append(avg_test_rmse.item())\n", "    else:\n", "        # 如果没有测试集，也填充NaN以保持数据对齐\n", "        history['test_loss'].append(avg_test_loss)\n", "        history['test_mae'].append(avg_test_mae)\n", "        history['test_rmse'].append(avg_test_rmse.item())\n", "\n", "    epoch_end_time = time.time()\n", "    epoch_duration = epoch_end_time - epoch_start_time\n", "    \n", "    # --- 汇报与日志记录 ---\n", "    log_entry = (\n", "        f\"{epoch + 1:5d} | {epoch_duration:14.2f}s | \"\n", "        f\"{avg_train_loss:10.6f} | {avg_test_loss:10.6f} | \"\n", "        f\"{avg_train_mae:10.6f} | {avg_train_rmse:10.6f} | \"\n", "        f\"{avg_test_mae:10.6f} | {avg_test_rmse:10.6f}\" \n", "    ).replace(\"nan\", \"N/A\".rjust(10)) # 美化输出\n", "\n", "    print(log_entry)\n", "    with open(LOG_FILE_PATH, 'a') as f:\n", "        f.write(log_entry + '\\n')\n", "\n", "    # --- 保存模型 Checkpoint ---\n", "    if (epoch + 1) % SAVE_EVERY_Q_EPOCHS == 0 or (epoch + 1) == NUM_EPOCHS:\n", "        os.makedirs(MODEL_SAVE_DIR, exist_ok=True)\n", "        save_path = os.path.join(MODEL_SAVE_DIR, f'checkpoint_epoch_{epoch+1}.pth')\n", "        \n", "        # 将所有需要恢复的状态打包进一个字典\n", "        checkpoint_data = {\n", "            'epoch': epoch + 1,\n", "            'model_state_dict': net.state_dict(),\n", "            'optimizer_state_dict': trainer.state_dict(),\n", "            'history': history\n", "        }\n", "        torch.save(checkpoint_data, save_path)\n", "        print(f\"Checkpoint saved: Full training state saved to {save_path}\")\n", "\n", "print(\"\\nTraining finished.\")"]}, {"cell_type": "code", "execution_count": null, "id": "9c8f16b8", "metadata": {}, "outputs": [], "source": ["\n", "# # ==============================================================================\n", "# # 4. 结果可视化 (Result Visualization)\n", "# # ==============================================================================\n", "\n", "# def plot_regression_metrics(history):\n", "#     \"\"\"根据history字典绘制回归任务的损失和MAE变化图\"\"\"\n", "#     fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 10), sharex=True)\n", "    \n", "#     epochs_ran = len(history['train_loss'])\n", "#     # 从1开始绘制X轴\n", "#     epoch_axis = range(start_epoch + 1, start_epoch + epochs_ran + 1) if start_epoch > 0 else range(1, epochs_ran + 1)\n", "\n", "\n", "#     # 绘制 Loss (MSE) 曲线\n", "#     ax1.plot(epoch_axis, history['train_loss'], 'o-', label='Train Loss (MSE)')\n", "#     #if not all(torch.isnan(torch.tensor(history['test_loss']))):\n", "#     #    ax1.plot(epoch_axis, history['test_loss'], 'o-', label='Test Loss (MSE)')\n", "#     ax1.set_ylabel('Loss (MSE)')\n", "#     ax1.set_title('Training Loss Curve')\n", "#     ax1.legend()\n", "#     ax1.grid(True)\n", "    \n", "#     # 绘制 Mean Absolute Error (MAE) 曲线\n", "#     ax2.plot(epoch_axis, history['train_mae'], 'o-', label='Train MAE')\n", "#     if not all(torch.isnan(torch.tensor(history['test_mae']))):\n", "#         ax2.plot(epoch_axis, history['test_mae'], 'o-', label='Test MAE')\n", "#     ax2.set_xlabel('Epochs')\n", "#     ax2.set_ylabel('Mean Absolute Error')\n", "#     ax2.set_title('Training and Test* MAE Curve')\n", "#     ax2.legend()\n", "#     ax2.grid(True)\n", "    \n", "#     plt.tight_layout()\n", "#     plt.savefig('training_curves.png')\n", "#     print(\"\\nMetrics plots have been saved to 'training_curves.png'\")\n", "#     plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "bf395295", "metadata": {}, "outputs": [], "source": ["# def plot_regression_metrics_2(history):\n", "#     \"\"\"根据history字典绘制回归任务的损失和MAE变化图，将MSE和MAE显示在同一个图中\"\"\"\n", "#     fig, ax1 = plt.subplots(figsize=(12, 8))\n", "    \n", "#     epochs_ran = len(history['train_loss'])\n", "#     # 从1开始绘制X轴\n", "#     epoch_axis = range(start_epoch + 1, start_epoch + epochs_ran + 1) if start_epoch > 0 else range(1, epochs_ran + 1)\n", "\n", "#     # 绘制 Loss (MSE) 曲线 - 使用左侧Y轴\n", "#     color = 'tab:blue'\n", "#     ax1.set_xlabel('Epochs')\n", "#     ax1.set_ylabel('Loss (MSE)', color=color)\n", "#     ax1.plot(epoch_axis, history['train_loss'], 'o-', color=color, label='Train Loss (MSE)')\n", "#     ax1.tick_params(axis='y', labelcolor=color)\n", "#     ax1.grid(True, alpha=0.3)\n", "    \n", "#     # 创建共享X轴的第二个Y轴\n", "#     ax2 = ax1.twinx()\n", "#     color = 'tab:red'\n", "#     ax2.set_ylabel('Mean Absolute Error', color=color)\n", "#     ax2.plot(epoch_axis, history['train_mae'], 's-', color=color, label='Train MAE')\n", "    \n", "#     # 如果有测试数据，也绘制出来\n", "#     if not all(torch.isnan(torch.tensor(history['test_mae']))):\n", "#         ax2.plot(epoch_axis, history['test_mae'], '^-', color='tab:green', label='Test MAE')\n", "#     ax2.tick_params(axis='y', labelcolor=color)\n", "    \n", "#     # 合并两个图例\n", "#     lines1, labels1 = ax1.get_legend_handles_labels()\n", "#     lines2, labels2 = ax2.get_legend_handles_labels()\n", "#     ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper right')\n", "    \n", "#     plt.title('Training metrics')\n", "#     plt.tight_layout()\n", "#     plt.savefig('training_curves.png')\n", "#     print(\"\\n指标图已保存至 'training_curves.png'\")\n", "#     plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "33e5cef2", "metadata": {}, "outputs": [], "source": ["# # # 只有在 history 不为空时才绘图\n", "# if history['train_loss']:\n", "#     plot_regression_metrics_2(history)"]}, {"cell_type": "code", "execution_count": null, "id": "e0efacaf", "metadata": {}, "outputs": [], "source": ["def plot_regression_metrics_from_log(log_file_path):\n", "    \"\"\"从日志文件读取并绘制回归任务的损失和MAE变化图，将MSE和MAE显示在同一个图中\"\"\"\n", "    # 读取日志文件\n", "    with open(log_file_path, 'r') as f:\n", "        lines = f.readlines()\n", "    \n", "    # 跳过标题行\n", "    data_lines = lines[1:]\n", "    \n", "    # 解析数据\n", "    epochs = []\n", "    train_loss = []\n", "    test_loss = []\n", "    train_mae = []\n", "    train_rmse = []\n", "    test_mae = []\n", "    test_rmse = []\n", "    \n", "    for line in data_lines:\n", "        parts = line.strip().split('|')\n", "        if len(parts) >= 8:\n", "            epochs.append(int(parts[0].strip()))\n", "            train_loss.append(float(parts[2].strip()) if parts[2].strip() != 'N/A' else float('nan'))\n", "            test_loss.append(float(parts[3].strip()) if parts[3].strip() != 'N/A' else float('nan'))\n", "            train_mae.append(float(parts[4].strip()) if parts[4].strip() != 'N/A' else float('nan'))\n", "            train_rmse.append(float(parts[5].strip()) if parts[5].strip() != 'N/A' else float('nan'))\n", "            test_mae.append(float(parts[6].strip()) if parts[6].strip() != 'N/A' else float('nan'))\n", "            test_rmse.append(float(parts[7].strip()) if parts[7].strip() != 'N/A' else float('nan'))\n", "    \n", "    # 绘图\n", "    fig, ax1 = plt.subplots(figsize=(12, 8))\n", "    \n", "    # 绘制 Loss (MSE) 曲线 - 使用左侧Y轴\n", "    color = 'tab:grey'\n", "    ax1.set_xlabel('Epochs')\n", "    ax1.set_ylabel('Loss (MSE)', color=color)\n", "    ax1.plot(epochs, train_loss, 'o-', color=color, label='Train Loss (MSE)')\n", "    ax1.tick_params(axis='y', labelcolor=color)\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 创建共享X轴的第二个Y轴\n", "    ax2 = ax1.twinx()\n", "    color = 'tab:grey'\n", "    ax2.set_ylabel('MAE/RMSE', color=color)\n", "    ax2.plot(epochs, train_mae, 's-', color='tab:red', label='Train MAE')\n", "    ax2.plot(epochs, train_rmse, 'v-', color='tab:orange', label='Train RMSE')\n", "    \n", "    # 如果有测试数据，也绘制出来\n", "    if not all(torch.isnan(torch.tensor(test_mae))):\n", "        ax2.plot(epochs, test_mae, '^-', color='tab:green', label='Test MAE')\n", "        ax2.plot(epochs, test_rmse, 'p-', color='tab:purple', label='Test RMSE')\n", "    ax2.tick_params(axis='y', labelcolor=color)\n", "    \n", "    # 合并两个图例\n", "    lines1, labels1 = ax1.get_legend_handles_labels()\n", "    lines2, labels2 = ax2.get_legend_handles_labels()\n", "    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper right')\n", "    \n", "    plt.title('Training metrics')\n", "    plt.tight_layout()\n", "    plt.savefig('training_curves.png')\n", "    print(\"\\n指标图已保存至 'training_curves.png'\")\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "73ca7b0a", "metadata": {}, "outputs": [], "source": ["plot_regression_metrics_from_log('./logs/training_log_regression.txt')"]}, {"cell_type": "code", "execution_count": null, "id": "b426801e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}