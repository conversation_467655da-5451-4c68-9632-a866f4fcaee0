import numpy as np
import pandas as pd


import os
import time
import matplotlib.pyplot as plt
import sys

def plot_regression_metrics_from_log(log_file_path):
    """从日志文件读取并绘制回归任务的损失和MAE变化图，将MSE和MAE显示在同一个图中"""
    # 读取日志文件
    with open(log_file_path, 'r') as f:
        lines = f.readlines()
    
    # 跳过标题行
    data_lines = lines[1:]
    
    # 解析数据
    epochs = []
    train_loss = []
    test_loss = []
    train_mae = []
    train_rmse = []
    test_mae = []
    test_rmse = []
    
    for line in data_lines:
        parts = line.strip().split('|')
        if len(parts) >= 8:
            epochs.append(int(parts[0].strip()))
            train_loss.append(float(parts[2].strip()) if parts[2].strip() != 'N/A' else float('nan'))
            test_loss.append(float(parts[3].strip()) if parts[3].strip() != 'N/A' else float('nan'))
            train_mae.append(float(parts[4].strip()) if parts[4].strip() != 'N/A' else float('nan'))
            train_rmse.append(float(parts[5].strip()) if parts[5].strip() != 'N/A' else float('nan'))
            test_mae.append(float(parts[6].strip()) if parts[6].strip() != 'N/A' else float('nan'))
            test_rmse.append(float(parts[7].strip()) if parts[7].strip() != 'N/A' else float('nan'))
    
    # 绘图
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # 绘制 Loss (MSE) 曲线 - 使用左侧Y轴
    color = 'tab:grey'
    ax1.set_xlabel('Epochs')
    ax1.set_ylabel('Loss (MAE)', color=color)
    ax1.plot(epochs, train_loss, 'o-', color=color, label='Train Loss (MAE)', markersize=12)
    ax1.tick_params(axis='y', labelcolor=color)
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 4)  # 设置左侧Y轴范围为0-5
    
    # 创建共享X轴的第二个Y轴
    ax2 = ax1.twinx()
    color = 'tab:grey'
    ax2.set_ylabel('Train/Test MAE/RMSE (kcal/mol)', color=color)
    ax2.plot(epochs, train_mae, 's-', color='tab:red', label='Train MAE')
    ax2.plot(epochs, train_rmse, 'v-', color='tab:orange', label='Train RMSE')
    
    # 如果有测试数据，也绘制出来
    ax2.plot(epochs, test_mae, '^-', color='tab:green', label='Test MAE')
    ax2.plot(epochs, test_rmse, 'p-', color='tab:purple', label='Test RMSE')
    ax2.tick_params(axis='y', labelcolor=color)
    ax2.set_ylim(0, 4)  # 设置右侧Y轴范围为0-5
    
    # 合并两个图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper right')
    
    plt.title('Training metrics')
    plt.tight_layout()
    plt.savefig('training_curves.png')
    print("\n指标图已保存至 'training_curves.png'")
    plt.show()


if __name__ == "__main__":
    plot_regression_metrics_from_log(sys.argv[1])



