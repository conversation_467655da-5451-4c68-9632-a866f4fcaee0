import torch
from torch import nn
from torch.nn import functional as F
from torch.utils import data



class SeparableConv3d(nn.Module):
    """
    一个完整的3D 深度可分离卷积模块
    包含: 深度卷积 -> [BN -> ReLU] -> 逐点卷积 
    """
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=False, middle_bn_relu=False):
        """
        :param in_channels: 输入通道数
        :param out_channels: 输出通道数
        :param kernel_size: 深度卷积的核大小
        :param stride: 深度卷积的步长
        :param padding: 深度卷积的填充
        """
        super(SeparableConv3d, self).__init__()
        

            # --- 1. 深度卷积 (Depthwise Convolution) ---
            # groups=in_channels 确保了每个输入通道都有自己独立的卷积核
            # 输出通道数必须等于输入通道数
            # 通常不在深度卷积后加偏置，因为BN层会进行中心化处理
        if middle_bn_relu:
            self.depthwise_conv = nn.Sequential(
                nn.Conv3d(in_channels, in_channels, kernel_size, stride, padding, groups=in_channels, bias=bias),
                nn.BatchNorm3d(in_channels),
                nn.ReLU())
        else:
            self.depthwise_conv = nn.Sequential(
                nn.Conv3d(in_channels, in_channels, kernel_size, stride, padding, groups=in_channels, bias=bias))     
            
            # --- 2. 逐点卷积 (Pointwise Convolution) ---
            # 1x1的标准卷积，用于组合通道特征
            # 同样，通常不加偏置
        
        self.pointwise_conv = nn.Sequential(
            nn.Conv3d(in_channels, out_channels, kernel_size=1, stride=1, padding=0, bias=bias))
        

    def forward(self, x):
        x = self.depthwise_conv(x)
        x = self.pointwise_conv(x)
        return x




class ResidualLayer(nn.Module):  
    def __init__(self, input_channels, output_channels,
                 kernel_size=3, strides=1, padding=1, cardinality=16):
        super().__init__()

        self.cardinality = cardinality


        self.BN_ReLU_Conv3D_in = nn.Sequential(nn.BatchNorm3d(input_channels), \
                                               nn.ReLU(), \
                                               nn.Conv3d(input_channels, input_channels, kernel_size=1, stride=1, 
                                               bias=False))

        self.BN_ReLU_Conv3D_out = nn.Sequential(nn.BatchNorm3d(input_channels), \
                                                nn.ReLU(), \
                                                nn.Conv3d(input_channels, output_channels, kernel_size=1, stride=1, 
                                                bias=False))

        self.sub_channels = input_channels // cardinality 

        self.BN_ReLU = nn.Sequential(nn.BatchNorm3d(input_channels), \
                                     nn.ReLU()) # after BN_ReLU_Conv3D_in

        #self.conv1 = nn.Conv3d(input_channels, self.sub_channels, kernel_size=3, stride=1, padding=1) 
        self.parallel_convs = nn.ModuleList()
        for _ in range(self.cardinality):
            self.parallel_convs.append(
                SeparableConv3d(
                    in_channels=input_channels,
                    out_channels=self.sub_channels,
                    kernel_size=kernel_size,
                    stride=strides,
                    padding=padding,
                    bias=False
                )
            )
            # self.parallel_convs.append(
            #     nn.Conv3d(
            #         in_channels=input_channels,
            #         out_channels=self.sub_channels,
            #         kernel_size=kernel_size,
            #         stride=strides,
            #         padding=padding
            #     )
            # )


        
    def forward(self, X):

        Y1 = self.BN_ReLU_Conv3D_in(X)
        Y1 = self.BN_ReLU(Y1)

        branch_outputs = []
        for conv_layer in self.parallel_convs:
            branch_outputs.append(conv_layer(Y1))
        Y1 = torch.cat(branch_outputs, dim=1)

        Y1 = self.BN_ReLU_Conv3D_out(Y1)

        Y2 = self.BN_ReLU_Conv3D_out(X) # shortcut branch 
        Y1 += Y2

        return F.relu(Y1)


def create_stacked_blocks(num_blocks):
    # 创建一个包含 num_blocks 个 BasicBlock 的 Python 列表
    # 注意：这里我们假设每个 block 的输入输出通道数相同
    blocks = [ResidualLayer(input_channels=64, output_channels=64, cardinality=16) for _ in range(num_blocks)]
    
    # 使用 * 将列表解包，传入 nn.Sequential
    return nn.Sequential(*blocks)


def init_weights(m):
    if isinstance(m, nn.Linear) or isinstance(m, nn.Conv2d) or isinstance(m, nn.Conv3d):
        # 使用 He Normal (Kaiming Normal) 初始化
        nn.init.kaiming_normal_(m.weight, mode='fan_in', nonlinearity='relu')
        if m.bias is not None:
            # 将偏置初始化为0
            nn.init.constant_(m.bias, 0)