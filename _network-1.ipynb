{"cells": [{"cell_type": "code", "execution_count": 30, "id": "0545cecb", "metadata": {}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from torch.nn import functional as F\n", "\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from torch.utils import data\n"]}, {"cell_type": "code", "execution_count": 31, "id": "99597c40", "metadata": {}, "outputs": [{"data": {"text/plain": ["'2.7.0'"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.__version__"]}, {"cell_type": "code", "execution_count": 32, "id": "2dddc04f", "metadata": {}, "outputs": [], "source": ["\n", "class SeparableConv3d(nn.Module):\n", "    \"\"\"\n", "    一个完整的3D 深度可分离卷积模块\n", "    包含: 深度卷积 -> [BN -> ReLU] -> 逐点卷积 \n", "    \"\"\"\n", "    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=False, middle_bn_relu=False):\n", "        \"\"\"\n", "        :param in_channels: 输入通道数\n", "        :param out_channels: 输出通道数\n", "        :param kernel_size: 深度卷积的核大小\n", "        :param stride: 深度卷积的步长\n", "        :param padding: 深度卷积的填充\n", "        \"\"\"\n", "        super(SeparableConv3d, self).__init__()\n", "        \n", "\n", "            # --- 1. 深度卷积 (Depthwise Convolution) ---\n", "            # groups=in_channels 确保了每个输入通道都有自己独立的卷积核\n", "            # 输出通道数必须等于输入通道数\n", "            # 通常不在深度卷积后加偏置，因为BN层会进行中心化处理\n", "        if middle_bn_relu:\n", "            self.depthwise_conv = nn.Sequential(\n", "                nn.Conv3d(in_channels, in_channels, kernel_size, stride, padding, groups=in_channels, bias=bias),\n", "                nn.BatchNorm3d(in_channels),\n", "                nn.ReLU())\n", "        else:\n", "            self.depthwise_conv = nn.Sequential(\n", "                nn.Conv3d(in_channels, in_channels, kernel_size, stride, padding, groups=in_channels, bias=bias))     \n", "            \n", "            # --- 2. 逐点卷积 (Pointwise Convolution) ---\n", "            # 1x1的标准卷积，用于组合通道特征\n", "            # 同样，通常不加偏置\n", "        \n", "        self.pointwise_conv = nn.Sequential(\n", "            nn.Conv3d(in_channels, out_channels, kernel_size=1, stride=1, padding=0, bias=bias))\n", "        \n", "\n", "    def forward(self, x):\n", "        x = self.depthwise_conv(x)\n", "        x = self.pointwise_conv(x)\n", "        return x"]}, {"cell_type": "code", "execution_count": 33, "id": "c4a983ed", "metadata": {}, "outputs": [], "source": ["# b1 = nn.Sequential(nn.Conv3d(16, 64, kernel_size=5, stride=2, padding=2),\n", "#                    nn.<PERSON><PERSON><PERSON><PERSON>3<PERSON>(64), nn.<PERSON><PERSON><PERSON>())"]}, {"cell_type": "code", "execution_count": 34, "id": "a22373e5", "metadata": {}, "outputs": [], "source": ["b1 = nn.Sequential(SeparableConv3d(13, 64, kernel_size=5, stride=2, padding=2))  # first parameter is input_channels \n", "                    #nn.<PERSON><PERSON><PERSON><PERSON>3<PERSON>(64), nn.<PERSON><PERSON><PERSON>())"]}, {"cell_type": "code", "execution_count": 35, "id": "a83fd799", "metadata": {}, "outputs": [], "source": ["class ResidualLayer(nn.Module):  \n", "    def __init__(self, input_channels, output_channels,\n", "                 kernel_size=3, strides=1, padding=1, cardinality=16):\n", "        super().__init__()\n", "\n", "        self.cardinality = cardinality\n", "\n", "\n", "        self.BN_ReLU_Conv3D_in = nn.Sequential(nn.BatchNorm3d(input_channels), \\\n", "                                               nn.<PERSON><PERSON><PERSON>(), \\\n", "                                               nn.Conv3d(input_channels, input_channels, kernel_size=1, stride=1, \n", "                                               bias=False))\n", "\n", "        self.BN_ReLU_Conv3D_out = nn.Sequential(nn.BatchNorm3d(input_channels), \\\n", "                                                nn.<PERSON><PERSON><PERSON>(), \\\n", "                                                nn.Conv3d(input_channels, output_channels, kernel_size=1, stride=1, \n", "                                                bias=False))\n", "\n", "        self.sub_channels = input_channels // cardinality \n", "\n", "        self.BN_ReLU = nn.Sequential(nn.BatchNorm3d(input_channels), \\\n", "                                     nn.ReLU()) # after BN_ReLU_Conv3D_in\n", "\n", "        #self.conv1 = nn.Conv3d(input_channels, self.sub_channels, kernel_size=3, stride=1, padding=1) \n", "        self.parallel_convs = nn.ModuleList()\n", "        for _ in range(self.cardinality):\n", "            self.parallel_convs.append(\n", "                SeparableConv3d(\n", "                    in_channels=input_channels,\n", "                    out_channels=self.sub_channels,\n", "                    kernel_size=kernel_size,\n", "                    stride=strides,\n", "                    padding=padding,\n", "                    bias=False\n", "                )\n", "            )\n", "            # self.parallel_convs.append(\n", "            #     nn.Conv3d(\n", "            #         in_channels=input_channels,\n", "            #         out_channels=self.sub_channels,\n", "            #         kernel_size=kernel_size,\n", "            #         stride=strides,\n", "            #         padding=padding\n", "            #     )\n", "            # )\n", "\n", "\n", "        \n", "    def forward(self, X):\n", "\n", "        Y1 = self.BN_ReLU_Conv3D_in(X)\n", "        Y1 = self.BN_ReLU(Y1)\n", "\n", "        branch_outputs = []\n", "        for conv_layer in self.parallel_convs:\n", "            branch_outputs.append(conv_layer(Y1))\n", "        Y1 = torch.cat(branch_outputs, dim=1)\n", "\n", "        Y1 = self.BN_ReLU_Conv3D_out(Y1)\n", "\n", "        Y2 = self.BN_ReLU_Conv3D_out(X) # shortcut branch \n", "        Y1 += Y2\n", "\n", "        return <PERSON><PERSON>re<PERSON>(Y1)"]}, {"cell_type": "code", "execution_count": 36, "id": "c93a4c9b", "metadata": {}, "outputs": [], "source": ["def create_stacked_blocks(num_blocks):\n", "    # 创建一个包含 num_blocks 个 BasicBlock 的 Python 列表\n", "    # 注意：这里我们假设每个 block 的输入输出通道数相同\n", "    blocks = [ResidualLayer(input_channels=64, output_channels=64, cardinality=16) for _ in range(num_blocks)]\n", "    \n", "    # 使用 * 将列表解包，传入 nn.Sequential\n", "    return nn.Sequential(*blocks)"]}, {"cell_type": "code", "execution_count": 37, "id": "fceeec6b", "metadata": {}, "outputs": [], "source": ["stacked_RLx10 = create_stacked_blocks(10) # 10 "]}, {"cell_type": "code", "execution_count": null, "id": "5cc959cc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 38, "id": "19c1bd9e", "metadata": {}, "outputs": [], "source": ["net = nn.Sequential(b1, \\\n", "                    stacked_RLx10, \\\n", "                    nn.MaxPool3d(kernel_size=3, stride=2, padding=0), \\\n", "                    ResidualLayer(input_channels=64, output_channels=256), \\\n", "                    ResidualLayer(input_channels=256, output_channels=256), \\\n", "                    ResidualLayer(input_channels=256, output_channels=256), \\\n", "                    ResidualLayer(input_channels=256, output_channels=256), \\\n", "                    nn.MaxPool3d(kernel_size=3, stride=2, padding=0), \\\n", "                    ResidualLayer(input_channels=256, output_channels=512), \\\n", "                    nn.Dropout(0.2), \\\n", "                    nn.AdaptiveAvgPool3d(1), \\\n", "                    nn.<PERSON>(), \\\n", "                    nn.<PERSON><PERSON>(512, 1)\n", "                    )\n", "\n", "# ResidualLayer(input_channels=64, output_channels=64, cardinality=16))"]}, {"cell_type": "code", "execution_count": 39, "id": "c4fcdcd5", "metadata": {}, "outputs": [], "source": ["X = torch.rand(size=(1, 13, 30, 30, 30))"]}, {"cell_type": "code", "execution_count": 40, "id": "dd35270a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sequential output shape:\t torch.Size([1, 64, 15, 15, 15])\n", "Sequential output shape:\t torch.Size([1, 64, 15, 15, 15])\n", "MaxPool3d output shape:\t torch.Size([1, 64, 7, 7, 7])\n", "ResidualLayer output shape:\t torch.Size([1, 256, 7, 7, 7])\n", "ResidualLayer output shape:\t torch.Size([1, 256, 7, 7, 7])\n", "ResidualLayer output shape:\t torch.Size([1, 256, 7, 7, 7])\n", "ResidualLayer output shape:\t torch.Size([1, 256, 7, 7, 7])\n", "MaxPool3d output shape:\t torch.Size([1, 256, 3, 3, 3])\n", "ResidualLayer output shape:\t torch.Size([1, 512, 3, 3, 3])\n", "Dropout output shape:\t torch.Size([1, 512, 3, 3, 3])\n", "AdaptiveAvgPool3d output shape:\t torch.Size([1, 512, 1, 1, 1])\n", "Flatten output shape:\t torch.Size([1, 512])\n", "Linear output shape:\t torch.Size([1, 1])\n"]}], "source": ["for layer in net:\n", "    X = layer(X)\n", "    print(layer.__class__.__name__,'output shape:\\t', X.shape)"]}, {"cell_type": "code", "execution_count": 41, "id": "7987822c", "metadata": {}, "outputs": [{"data": {"text/plain": ["===============================================================================================\n", "Layer (type:depth-idx)                        Output Shape              Param #\n", "===============================================================================================\n", "Sequential                                    [1, 1]                    --\n", "├─Sequential: 1-1                             [1, 64, 15, 15, 15]       --\n", "│    └─SeparableConv3d: 2-1                   [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-1                   [1, 13, 15, 15, 15]       1,625\n", "│    │    └─Sequential: 3-2                   [1, 64, 15, 15, 15]       832\n", "├─Sequential: 1-2                             [1, 64, 15, 15, 15]       --\n", "│    └─ResidualLayer: 2-2                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-3                   [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-4                   [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-5                   --                        31,744\n", "│    │    └─Sequential: 3-6                   [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-7                   [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-3                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-8                   [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-9                   [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-10                  --                        31,744\n", "│    │    └─Sequential: 3-11                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-12                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-4                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-13                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-14                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-15                  --                        31,744\n", "│    │    └─Sequential: 3-16                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-17                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-5                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-18                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-19                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-20                  --                        31,744\n", "│    │    └─Sequential: 3-21                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-22                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-6                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-23                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-24                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-25                  --                        31,744\n", "│    │    └─Sequential: 3-26                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-27                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-7                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-28                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-29                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-30                  --                        31,744\n", "│    │    └─Sequential: 3-31                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-32                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-8                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-33                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-34                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-35                  --                        31,744\n", "│    │    └─Sequential: 3-36                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-37                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-9                     [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-38                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-39                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-40                  --                        31,744\n", "│    │    └─Sequential: 3-41                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-42                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-10                    [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-43                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-44                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-45                  --                        31,744\n", "│    │    └─Sequential: 3-46                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-47                  [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-11                    [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-48                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-49                  [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-50                  --                        31,744\n", "│    │    └─Sequential: 3-51                  [1, 64, 15, 15, 15]       4,224\n", "│    │    └─Sequential: 3-52                  [1, 64, 15, 15, 15]       (recursive)\n", "├─MaxPool3d: 1-3                              [1, 64, 7, 7, 7]          --\n", "├─ResidualLayer: 1-4                          [1, 256, 7, 7, 7]         --\n", "│    └─Sequential: 2-12                       [1, 64, 7, 7, 7]          --\n", "│    │    └─BatchNorm3d: 3-53                 [1, 64, 7, 7, 7]          128\n", "│    │    └─ReLU: 3-54                        [1, 64, 7, 7, 7]          --\n", "│    │    └─Conv3d: 3-55                      [1, 64, 7, 7, 7]          4,096\n", "│    └─Sequential: 2-13                       [1, 64, 7, 7, 7]          --\n", "│    │    └─BatchNorm3d: 3-56                 [1, 64, 7, 7, 7]          128\n", "│    │    └─ReLU: 3-57                        [1, 64, 7, 7, 7]          --\n", "│    └─ModuleList: 2-14                       --                        --\n", "│    │    └─SeparableConv3d: 3-58             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-59             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-60             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-61             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-62             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-63             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-64             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-65             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-66             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-67             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-68             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-69             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-70             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-71             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-72             [1, 4, 7, 7, 7]           1,984\n", "│    │    └─SeparableConv3d: 3-73             [1, 4, 7, 7, 7]           1,984\n", "│    └─Sequential: 2-15                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-74                 [1, 64, 7, 7, 7]          128\n", "│    │    └─ReLU: 3-75                        [1, 64, 7, 7, 7]          --\n", "│    │    └─Conv3d: 3-76                      [1, 256, 7, 7, 7]         16,384\n", "│    └─Sequential: 2-16                       [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─BatchNorm3d: 3-77                 [1, 64, 7, 7, 7]          (recursive)\n", "│    │    └─ReLU: 3-78                        [1, 64, 7, 7, 7]          --\n", "│    │    └─Conv3d: 3-79                      [1, 256, 7, 7, 7]         (recursive)\n", "├─ResidualLayer: 1-5                          [1, 256, 7, 7, 7]         --\n", "│    └─Sequential: 2-17                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-80                 [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-81                        [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-82                      [1, 256, 7, 7, 7]         65,536\n", "│    └─Sequential: 2-18                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-83                 [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-84                        [1, 256, 7, 7, 7]         --\n", "│    └─ModuleList: 2-19                       --                        --\n", "│    │    └─SeparableConv3d: 3-85             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-86             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-87             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-88             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-89             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-90             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-91             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-92             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-93             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-94             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-95             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-96             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-97             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-98             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-99             [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-100            [1, 16, 7, 7, 7]          11,008\n", "│    └─Sequential: 2-20                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-101                [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-102                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-103                     [1, 256, 7, 7, 7]         65,536\n", "│    └─Sequential: 2-21                       [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─BatchNorm3d: 3-104                [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─ReLU: 3-105                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-106                     [1, 256, 7, 7, 7]         (recursive)\n", "├─ResidualLayer: 1-6                          [1, 256, 7, 7, 7]         --\n", "│    └─Sequential: 2-22                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-107                [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-108                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-109                     [1, 256, 7, 7, 7]         65,536\n", "│    └─Sequential: 2-23                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-110                [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-111                       [1, 256, 7, 7, 7]         --\n", "│    └─ModuleList: 2-24                       --                        --\n", "│    │    └─SeparableConv3d: 3-112            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-113            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-114            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-115            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-116            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-117            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-118            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-119            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-120            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-121            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-122            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-123            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-124            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-125            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-126            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-127            [1, 16, 7, 7, 7]          11,008\n", "│    └─Sequential: 2-25                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-128                [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-129                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-130                     [1, 256, 7, 7, 7]         65,536\n", "│    └─Sequential: 2-26                       [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─BatchNorm3d: 3-131                [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─ReLU: 3-132                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-133                     [1, 256, 7, 7, 7]         (recursive)\n", "├─ResidualLayer: 1-7                          [1, 256, 7, 7, 7]         --\n", "│    └─Sequential: 2-27                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-134                [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-135                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-136                     [1, 256, 7, 7, 7]         65,536\n", "│    └─Sequential: 2-28                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-137                [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-138                       [1, 256, 7, 7, 7]         --\n", "│    └─ModuleList: 2-29                       --                        --\n", "│    │    └─SeparableConv3d: 3-139            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-140            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-141            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-142            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-143            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-144            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-145            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-146            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-147            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-148            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-149            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-150            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-151            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-152            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-153            [1, 16, 7, 7, 7]          11,008\n", "│    │    └─SeparableConv3d: 3-154            [1, 16, 7, 7, 7]          11,008\n", "│    └─Sequential: 2-30                       [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-155                [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-156                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-157                     [1, 256, 7, 7, 7]         65,536\n", "│    └─Sequential: 2-31                       [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─BatchNorm3d: 3-158                [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─ReLU: 3-159                       [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-160                     [1, 256, 7, 7, 7]         (recursive)\n", "├─MaxPool3d: 1-8                              [1, 256, 3, 3, 3]         --\n", "├─ResidualLayer: 1-9                          [1, 512, 3, 3, 3]         --\n", "│    └─Sequential: 2-32                       [1, 256, 3, 3, 3]         --\n", "│    │    └─BatchNorm3d: 3-161                [1, 256, 3, 3, 3]         512\n", "│    │    └─ReLU: 3-162                       [1, 256, 3, 3, 3]         --\n", "│    │    └─Conv3d: 3-163                     [1, 256, 3, 3, 3]         65,536\n", "│    └─Sequential: 2-33                       [1, 256, 3, 3, 3]         --\n", "│    │    └─BatchNorm3d: 3-164                [1, 256, 3, 3, 3]         512\n", "│    │    └─ReLU: 3-165                       [1, 256, 3, 3, 3]         --\n", "│    └─ModuleList: 2-34                       --                        --\n", "│    │    └─SeparableConv3d: 3-166            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-167            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-168            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-169            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-170            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-171            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-172            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-173            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-174            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-175            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-176            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-177            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-178            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-179            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-180            [1, 16, 3, 3, 3]          11,008\n", "│    │    └─SeparableConv3d: 3-181            [1, 16, 3, 3, 3]          11,008\n", "│    └─Sequential: 2-35                       [1, 512, 3, 3, 3]         --\n", "│    │    └─BatchNorm3d: 3-182                [1, 256, 3, 3, 3]         512\n", "│    │    └─ReLU: 3-183                       [1, 256, 3, 3, 3]         --\n", "│    │    └─Conv3d: 3-184                     [1, 512, 3, 3, 3]         131,072\n", "│    └─Sequential: 2-36                       [1, 512, 3, 3, 3]         (recursive)\n", "│    │    └─BatchNorm3d: 3-185                [1, 256, 3, 3, 3]         (recursive)\n", "│    │    └─ReLU: 3-186                       [1, 256, 3, 3, 3]         --\n", "│    │    └─Conv3d: 3-187                     [1, 512, 3, 3, 3]         (recursive)\n", "├─Dropout: 1-10                               [1, 512, 3, 3, 3]         --\n", "├─AdaptiveAvgPool3d: 1-11                     [1, 512, 1, 1, 1]         --\n", "├─Flatten: 1-12                               [1, 512]                  --\n", "├─Linear: 1-13                                [1, 1]                    513\n", "===============================================================================================\n", "Total params: 1,759,258\n", "Trainable params: 1,759,258\n", "Non-trainable params: 0\n", "Total mult-adds (G): 1.92\n", "===============================================================================================\n", "Input size (MB): 1.40\n", "Forward/backward pass size (MB): 474.08\n", "Params size (MB): 7.04\n", "Estimated Total Size (MB): 482.52\n", "==============================================================================================="]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["from torchinfo import summary\n", "summary(net, input_size=(1, 13, 30, 30, 30))  # 输入张量的形状"]}, {"cell_type": "code", "execution_count": 42, "id": "559571e5", "metadata": {}, "outputs": [], "source": ["\n", "class Efficient3DDataset(Dataset):\n", "    def __init__(self, csv_file, root_dir, transform=None):\n", "        \"\"\"\n", "        Args:\n", "            csv_file (string): 包含文件名和标签的CSV文件路径。\n", "            root_dir (string): 存放所有处理后 .npy 文件的目录。\n", "            transform (callable, optional): 应用于样本的转换操作。\n", "        \"\"\"\n", "        self.labels_frame = pd.read_csv(csv_file)\n", "        self.root_dir = root_dir\n", "        self.transform = transform\n", "\n", "    def __len__(self):\n", "        return len(self.labels_frame)\n", "\n", "    def __getitem__(self, idx):\n", "        # 1. 从DataFrame获取文件名和标签\n", "        filename = self.labels_frame.iloc[idx, 0]\n", "        label = self.labels_frame.iloc[idx, 1]\n", "        \n", "        # 2. 读取单个.npy文件（一次IO操作，非常快）\n", "        filepath = os.path.join(self.root_dir, filename)\n", "        x_numpy = np.load(filepath) # 直接得到 (13, 30, 30, 30) 的数组\n", "        \n", "        # 3. 转换为Tensor\n", "        x_tensor = torch.from_numpy(x_numpy).float()\n", "        y_tensor = torch.tensor(label, dtype=torch.float)\n", "\n", "        if self.transform:\n", "            x_tensor = self.transform(x_tensor)\n", "            \n", "        return x_tensor, y_tensor"]}, {"cell_type": "code", "execution_count": 43, "id": "a11539b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset created successfully. Total samples: 6840\n"]}], "source": ["train_dataset = Efficient3DDataset(csv_file='~/research/other-repos/arontier-PDBbind-2020/core-set_junsu-pdbqt/labels.csv', root_dir='~/research/other-repos/arontier-PDBbind-2020/core-set_junsu-pdbqt/')\n", "print(f\"Dataset created successfully. Total samples: {len(train_dataset)}\")\n"]}, {"cell_type": "code", "execution_count": 44, "id": "e575b82e", "metadata": {}, "outputs": [], "source": ["train_iter = data.DataLoader(dataset=train_dataset, batch_size=1, shuffle=True, num_workers=2) "]}, {"cell_type": "code", "execution_count": null, "id": "e0ce1bdd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}