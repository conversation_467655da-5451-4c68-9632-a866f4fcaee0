{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0545cecb", "metadata": {}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from torch.nn import functional as F"]}, {"cell_type": "code", "execution_count": 2, "id": "99597c40", "metadata": {}, "outputs": [{"data": {"text/plain": ["'2.7.0'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.__version__"]}, {"cell_type": "code", "execution_count": 3, "id": "c4a983ed", "metadata": {}, "outputs": [], "source": ["b1 = nn.Sequential(nn.Conv3d(16, 64, kernel_size=5, stride=2, padding=2),\n", "                   nn.<PERSON><PERSON><PERSON><PERSON>3<PERSON>(64), nn.<PERSON><PERSON><PERSON>())"]}, {"cell_type": "code", "execution_count": null, "id": "a83fd799", "metadata": {}, "outputs": [], "source": ["class ResidualLayer(nn.Module):  \n", "    def __init__(self, input_channels, output_channels,\n", "                 kernel_size=3, strides=1, padding=1, cardinality=16):\n", "        super().__init__()\n", "\n", "        self.cardinality = cardinality\n", "\n", "\n", "        self.BN_ReLU_Conv3D_in = nn.Sequential(nn.BatchNorm3d(input_channels), \\\n", "                                               nn.<PERSON><PERSON><PERSON>(), \\\n", "                                               nn.Conv3d(input_channels, input_channels, kernel_size=1, stride=1))\n", "\n", "        self.BN_ReLU_Conv3D_out = nn.Sequential(nn.BatchNorm3d(input_channels), \\\n", "                                                nn.<PERSON><PERSON><PERSON>(), \\``\n", "                                                nn.Conv3d(input_channels, output_channels, kernel_size=1, stride=1))\n", "\n", "        self.sub_channels = input_channels // cardinality \n", "\n", "        self.BN_ReLU = nn.Sequential(nn.BatchNorm3d(input_channels), \\\n", "                                     nn.ReLU())\n", "\n", "        #self.conv1 = nn.Conv3d(input_channels, self.sub_channels, kernel_size=3, stride=1, padding=1) \n", "        self.parallel_convs = nn.ModuleList()\n", "        for _ in range(self.cardinality):\n", "            self.parallel_convs.append(\n", "                nn.Conv3d(\n", "                    in_channels=input_channels,\n", "                    out_channels=self.sub_channels,\n", "                    kernel_size=kernel_size,\n", "                    stride=strides,\n", "                    padding=padding\n", "                )\n", "            )\n", "\n", "\n", "        \n", "    def forward(self, X):\n", "\n", "        Y1 = self.BN_ReLU_Conv3D_in(X)\n", "        Y1 = self.BN_ReLU(Y1)\n", "\n", "        branch_outputs = []\n", "        for conv_layer in self.parallel_convs:\n", "            branch_outputs.append(conv_layer(Y1))\n", "        Y1 = torch.cat(branch_outputs, dim=1)\n", "\n", "        Y1 = self.BN_ReLU_Conv3D_out(Y1)\n", "\n", "        Y2 = self.BN_ReLU_Conv3D_out(X) # shortcut branch \n", "        Y1 += Y2\n", "\n", "        return <PERSON><PERSON>re<PERSON>(Y1)"]}, {"cell_type": "code", "execution_count": 5, "id": "c93a4c9b", "metadata": {}, "outputs": [], "source": ["def create_stacked_blocks(num_blocks):\n", "    # 创建一个包含 num_blocks 个 BasicBlock 的 Python 列表\n", "    # 注意：这里我们假设每个 block 的输入输出通道数相同\n", "    blocks = [ResidualLayer(input_channels=64, output_channels=64, cardinality=16) for _ in range(num_blocks)]\n", "    \n", "    # 使用 * 将列表解包，传入 nn.Sequential\n", "    return nn.Sequential(*blocks)"]}, {"cell_type": "code", "execution_count": 6, "id": "fceeec6b", "metadata": {}, "outputs": [], "source": ["stacked_RLx10 = create_stacked_blocks(10) # 10 "]}, {"cell_type": "code", "execution_count": null, "id": "5cc959cc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "id": "19c1bd9e", "metadata": {}, "outputs": [], "source": ["net = nn.Sequential(b1, \\\n", "                    stacked_RLx10, \\\n", "                    nn.MaxPool3d(kernel_size=3, stride=2, padding=0), \\\n", "                    ResidualLayer(input_channels=64, output_channels=256), \\\n", "                    ResidualLayer(input_channels=256, output_channels=256), \\\n", "                    ResidualLayer(input_channels=256, output_channels=256), \\\n", "                    ResidualLayer(input_channels=256, output_channels=256), \\\n", "                    nn.MaxPool3d(kernel_size=3, stride=2, padding=0), \\\n", "                    ResidualLayer(input_channels=256, output_channels=512), \\\n", "                    nn.Dropout(0.2), \\\n", "                    nn.AdaptiveAvgPool3d(1), \\\n", "                    nn.<PERSON>(), \\\n", "                    nn.<PERSON><PERSON>(512, 1)\n", "                    )\n", "\n", "# ResidualLayer(input_channels=64, output_channels=64, cardinality=16))"]}, {"cell_type": "code", "execution_count": 8, "id": "c4fcdcd5", "metadata": {}, "outputs": [], "source": ["X = torch.rand(size=(1, 16, 30, 30, 30))"]}, {"cell_type": "code", "execution_count": 9, "id": "dd35270a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sequential output shape:\t torch.Size([1, 64, 15, 15, 15])\n", "Sequential output shape:\t torch.Size([1, 64, 15, 15, 15])\n", "MaxPool3d output shape:\t torch.Size([1, 64, 7, 7, 7])\n", "ResidualLayer output shape:\t torch.Size([1, 256, 7, 7, 7])\n", "ResidualLayer output shape:\t torch.Size([1, 256, 7, 7, 7])\n", "ResidualLayer output shape:\t torch.Size([1, 256, 7, 7, 7])\n", "ResidualLayer output shape:\t torch.Size([1, 256, 7, 7, 7])\n", "MaxPool3d output shape:\t torch.Size([1, 256, 3, 3, 3])\n", "ResidualLayer output shape:\t torch.Size([1, 512, 3, 3, 3])\n", "Dropout output shape:\t torch.Size([1, 512, 3, 3, 3])\n", "AdaptiveAvgPool3d output shape:\t torch.Size([1, 512, 1, 1, 1])\n", "Flatten output shape:\t torch.Size([1, 512])\n", "Linear output shape:\t torch.Size([1, 1])\n"]}], "source": ["for layer in net:\n", "    X = layer(X)\n", "    print(layer.__class__.__name__,'output shape:\\t', X.shape)"]}, {"cell_type": "code", "execution_count": 10, "id": "3d35cfe7", "metadata": {}, "outputs": [], "source": ["#print(net)"]}, {"cell_type": "code", "execution_count": null, "id": "5a1a2cdc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 11, "id": "7987822c", "metadata": {}, "outputs": [{"data": {"text/plain": ["==========================================================================================\n", "Layer (type:depth-idx)                   Output Shape              Param #\n", "==========================================================================================\n", "Sequential                               [1, 1]                    --\n", "├─Sequential: 1-1                        [1, 64, 15, 15, 15]       --\n", "│    └─Conv3d: 2-1                       [1, 64, 15, 15, 15]       128,064\n", "│    └─BatchNorm3d: 2-2                  [1, 64, 15, 15, 15]       128\n", "│    └─ReLU: 2-3                         [1, 64, 15, 15, 15]       --\n", "├─Sequential: 1-2                        [1, 64, 15, 15, 15]       --\n", "│    └─ResidualLayer: 2-4                [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-1              [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-2              [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-3              --                        110,656\n", "│    │    └─Sequential: 3-4              [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-5              [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-5                [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-6              [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-7              [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-8              --                        110,656\n", "│    │    └─Sequential: 3-9              [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-10             [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-6                [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-11             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-12             [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-13             --                        110,656\n", "│    │    └─Sequential: 3-14             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-15             [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-7                [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-16             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-17             [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-18             --                        110,656\n", "│    │    └─Sequential: 3-19             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-20             [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-8                [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-21             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-22             [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-23             --                        110,656\n", "│    │    └─Sequential: 3-24             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-25             [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-9                [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-26             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-27             [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-28             --                        110,656\n", "│    │    └─Sequential: 3-29             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-30             [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-10               [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-31             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-32             [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-33             --                        110,656\n", "│    │    └─Sequential: 3-34             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-35             [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-11               [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-36             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-37             [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-38             --                        110,656\n", "│    │    └─Sequential: 3-39             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-40             [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-12               [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-41             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-42             [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-43             --                        110,656\n", "│    │    └─Sequential: 3-44             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-45             [1, 64, 15, 15, 15]       (recursive)\n", "│    └─ResidualLayer: 2-13               [1, 64, 15, 15, 15]       --\n", "│    │    └─Sequential: 3-46             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-47             [1, 64, 15, 15, 15]       128\n", "│    │    └─ModuleList: 3-48             --                        110,656\n", "│    │    └─Sequential: 3-49             [1, 64, 15, 15, 15]       4,288\n", "│    │    └─Sequential: 3-50             [1, 64, 15, 15, 15]       (recursive)\n", "├─MaxPool3d: 1-3                         [1, 64, 7, 7, 7]          --\n", "├─ResidualLayer: 1-4                     [1, 256, 7, 7, 7]         --\n", "│    └─Sequential: 2-14                  [1, 64, 7, 7, 7]          --\n", "│    │    └─BatchNorm3d: 3-51            [1, 64, 7, 7, 7]          128\n", "│    │    └─ReLU: 3-52                   [1, 64, 7, 7, 7]          --\n", "│    │    └─Conv3d: 3-53                 [1, 64, 7, 7, 7]          4,160\n", "│    └─Sequential: 2-15                  [1, 64, 7, 7, 7]          --\n", "│    │    └─BatchNorm3d: 3-54            [1, 64, 7, 7, 7]          128\n", "│    │    └─ReLU: 3-55                   [1, 64, 7, 7, 7]          --\n", "│    └─ModuleList: 2-16                  --                        --\n", "│    │    └─Conv3d: 3-56                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-57                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-58                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-59                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-60                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-61                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-62                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-63                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-64                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-65                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-66                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-67                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-68                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-69                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-70                 [1, 4, 7, 7, 7]           6,916\n", "│    │    └─Conv3d: 3-71                 [1, 4, 7, 7, 7]           6,916\n", "│    └─Sequential: 2-17                  [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-72            [1, 64, 7, 7, 7]          128\n", "│    │    └─ReLU: 3-73                   [1, 64, 7, 7, 7]          --\n", "│    │    └─Conv3d: 3-74                 [1, 256, 7, 7, 7]         16,640\n", "│    └─Sequential: 2-18                  [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─BatchNorm3d: 3-75            [1, 64, 7, 7, 7]          (recursive)\n", "│    │    └─ReLU: 3-76                   [1, 64, 7, 7, 7]          --\n", "│    │    └─Conv3d: 3-77                 [1, 256, 7, 7, 7]         (recursive)\n", "├─ResidualLayer: 1-5                     [1, 256, 7, 7, 7]         --\n", "│    └─Sequential: 2-19                  [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-78            [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-79                   [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-80                 [1, 256, 7, 7, 7]         65,792\n", "│    └─Sequential: 2-20                  [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-81            [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-82                   [1, 256, 7, 7, 7]         --\n", "│    └─ModuleList: 2-21                  --                        --\n", "│    │    └─Conv3d: 3-83                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-84                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-85                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-86                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-87                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-88                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-89                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-90                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-91                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-92                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-93                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-94                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-95                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-96                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-97                 [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-98                 [1, 16, 7, 7, 7]          110,608\n", "│    └─Sequential: 2-22                  [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-99            [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-100                  [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-101                [1, 256, 7, 7, 7]         65,792\n", "│    └─Sequential: 2-23                  [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─BatchNorm3d: 3-102           [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─ReLU: 3-103                  [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-104                [1, 256, 7, 7, 7]         (recursive)\n", "├─ResidualLayer: 1-6                     [1, 256, 7, 7, 7]         --\n", "│    └─Sequential: 2-24                  [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-105           [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-106                  [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-107                [1, 256, 7, 7, 7]         65,792\n", "│    └─Sequential: 2-25                  [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-108           [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-109                  [1, 256, 7, 7, 7]         --\n", "│    └─ModuleList: 2-26                  --                        --\n", "│    │    └─Conv3d: 3-110                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-111                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-112                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-113                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-114                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-115                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-116                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-117                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-118                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-119                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-120                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-121                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-122                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-123                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-124                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-125                [1, 16, 7, 7, 7]          110,608\n", "│    └─Sequential: 2-27                  [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-126           [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-127                  [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-128                [1, 256, 7, 7, 7]         65,792\n", "│    └─Sequential: 2-28                  [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─BatchNorm3d: 3-129           [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─ReLU: 3-130                  [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-131                [1, 256, 7, 7, 7]         (recursive)\n", "├─ResidualLayer: 1-7                     [1, 256, 7, 7, 7]         --\n", "│    └─Sequential: 2-29                  [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-132           [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-133                  [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-134                [1, 256, 7, 7, 7]         65,792\n", "│    └─Sequential: 2-30                  [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-135           [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-136                  [1, 256, 7, 7, 7]         --\n", "│    └─ModuleList: 2-31                  --                        --\n", "│    │    └─Conv3d: 3-137                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-138                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-139                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-140                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-141                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-142                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-143                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-144                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-145                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-146                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-147                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-148                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-149                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-150                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-151                [1, 16, 7, 7, 7]          110,608\n", "│    │    └─Conv3d: 3-152                [1, 16, 7, 7, 7]          110,608\n", "│    └─Sequential: 2-32                  [1, 256, 7, 7, 7]         --\n", "│    │    └─BatchNorm3d: 3-153           [1, 256, 7, 7, 7]         512\n", "│    │    └─ReLU: 3-154                  [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-155                [1, 256, 7, 7, 7]         65,792\n", "│    └─Sequential: 2-33                  [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─BatchNorm3d: 3-156           [1, 256, 7, 7, 7]         (recursive)\n", "│    │    └─ReLU: 3-157                  [1, 256, 7, 7, 7]         --\n", "│    │    └─Conv3d: 3-158                [1, 256, 7, 7, 7]         (recursive)\n", "├─MaxPool3d: 1-8                         [1, 256, 3, 3, 3]         --\n", "├─ResidualLayer: 1-9                     [1, 512, 3, 3, 3]         --\n", "│    └─Sequential: 2-34                  [1, 256, 3, 3, 3]         --\n", "│    │    └─BatchNorm3d: 3-159           [1, 256, 3, 3, 3]         512\n", "│    │    └─ReLU: 3-160                  [1, 256, 3, 3, 3]         --\n", "│    │    └─Conv3d: 3-161                [1, 256, 3, 3, 3]         65,792\n", "│    └─Sequential: 2-35                  [1, 256, 3, 3, 3]         --\n", "│    │    └─BatchNorm3d: 3-162           [1, 256, 3, 3, 3]         512\n", "│    │    └─ReLU: 3-163                  [1, 256, 3, 3, 3]         --\n", "│    └─ModuleList: 2-36                  --                        --\n", "│    │    └─Conv3d: 3-164                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-165                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-166                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-167                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-168                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-169                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-170                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-171                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-172                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-173                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-174                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-175                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-176                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-177                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-178                [1, 16, 3, 3, 3]          110,608\n", "│    │    └─Conv3d: 3-179                [1, 16, 3, 3, 3]          110,608\n", "│    └─Sequential: 2-37                  [1, 512, 3, 3, 3]         --\n", "│    │    └─BatchNorm3d: 3-180           [1, 256, 3, 3, 3]         512\n", "│    │    └─ReLU: 3-181                  [1, 256, 3, 3, 3]         --\n", "│    │    └─Conv3d: 3-182                [1, 512, 3, 3, 3]         131,584\n", "│    └─Sequential: 2-38                  [1, 512, 3, 3, 3]         (recursive)\n", "│    │    └─BatchNorm3d: 3-183           [1, 256, 3, 3, 3]         (recursive)\n", "│    │    └─ReLU: 3-184                  [1, 256, 3, 3, 3]         --\n", "│    │    └─Conv3d: 3-185                [1, 512, 3, 3, 3]         (recursive)\n", "├─Dropout: 1-10                          [1, 512, 3, 3, 3]         --\n", "├─AdaptiveAvgPool3d: 1-11                [1, 512, 1, 1, 1]         --\n", "├─Flatten: 1-12                          [1, 512]                  --\n", "├─Linear: 1-13                           [1, 1]                    513\n", "==========================================================================================\n", "Total params: 9,131,329\n", "Trainable params: 9,131,329\n", "Non-trainable params: 0\n", "Total mult-adds (G): 6.72\n", "==========================================================================================\n", "Input size (MB): 1.73\n", "Forward/backward pass size (MB): 161.57\n", "Params size (MB): 36.53\n", "Estimated Total Size (MB): 199.82\n", "=========================================================================================="]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["from torchinfo import summary\n", "summary(net, input_size=(1, 16, 30, 30, 30))  # 输入张量的形状"]}, {"cell_type": "code", "execution_count": 12, "id": "9efd2a58", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------------------------------------------\n", "        Layer (type)               Output Shape         Param #\n", "================================================================\n", "            Conv3d-1       [-1, 64, 15, 15, 15]         128,064\n", "       BatchNorm3d-2       [-1, 64, 15, 15, 15]             128\n", "              ReLU-3       [-1, 64, 15, 15, 15]               0\n", "       BatchNorm3d-4       [-1, 64, 15, 15, 15]             128\n", "              ReLU-5       [-1, 64, 15, 15, 15]               0\n", "            Conv3d-6       [-1, 64, 15, 15, 15]           4,160\n", "       BatchNorm3d-7       [-1, 64, 15, 15, 15]             128\n", "              ReLU-8       [-1, 64, 15, 15, 15]               0\n", "            Conv3d-9        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-10        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-11        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-12        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-13        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-14        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-15        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-16        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-17        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-18        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-19        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-20        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-21        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-22        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-23        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-24        [-1, 4, 15, 15, 15]           6,916\n", "      BatchNorm3d-25       [-1, 64, 15, 15, 15]             128\n", "             ReLU-26       [-1, 64, 15, 15, 15]               0\n", "           Conv3d-27       [-1, 64, 15, 15, 15]           4,160\n", "      BatchNorm3d-28       [-1, 64, 15, 15, 15]             128\n", "             ReLU-29       [-1, 64, 15, 15, 15]               0\n", "           Conv3d-30       [-1, 64, 15, 15, 15]           4,160\n", "    Residual<PERSON><PERSON><PERSON>-31       [-1, 64, 15, 15, 15]               0\n", "      BatchNorm3d-32       [-1, 64, 15, 15, 15]             128\n", "             ReLU-33       [-1, 64, 15, 15, 15]               0\n", "           Conv3d-34       [-1, 64, 15, 15, 15]           4,160\n", "      BatchNorm3d-35       [-1, 64, 15, 15, 15]             128\n", "             ReLU-36       [-1, 64, 15, 15, 15]               0\n", "           Conv3d-37        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-38        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-39        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-40        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-41        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-42        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-43        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-44        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-45        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-46        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-47        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-48        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-49        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-50        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-51        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-52        [-1, 4, 15, 15, 15]           6,916\n", "      BatchNorm3d-53       [-1, 64, 15, 15, 15]             128\n", "             ReLU-54       [-1, 64, 15, 15, 15]               0\n", "           Conv3d-55       [-1, 64, 15, 15, 15]           4,160\n", "      BatchNorm3d-56       [-1, 64, 15, 15, 15]             128\n", "             ReLU-57       [-1, 64, 15, 15, 15]               0\n", "           Conv3d-58       [-1, 64, 15, 15, 15]           4,160\n", "    Residual<PERSON><PERSON><PERSON>-59       [-1, 64, 15, 15, 15]               0\n", "      BatchNorm3d-60       [-1, 64, 15, 15, 15]             128\n", "             ReLU-61       [-1, 64, 15, 15, 15]               0\n", "           Conv3d-62       [-1, 64, 15, 15, 15]           4,160\n", "      BatchNorm3d-63       [-1, 64, 15, 15, 15]             128\n", "             ReLU-64       [-1, 64, 15, 15, 15]               0\n", "           Conv3d-65        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-66        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-67        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-68        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-69        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-70        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-71        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-72        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-73        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-74        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-75        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-76        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-77        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-78        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-79        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-80        [-1, 4, 15, 15, 15]           6,916\n", "      BatchNorm3d-81       [-1, 64, 15, 15, 15]             128\n", "             ReLU-82       [-1, 64, 15, 15, 15]               0\n", "           Conv3d-83       [-1, 64, 15, 15, 15]           4,160\n", "      BatchNorm3d-84       [-1, 64, 15, 15, 15]             128\n", "             ReLU-85       [-1, 64, 15, 15, 15]               0\n", "           Conv3d-86       [-1, 64, 15, 15, 15]           4,160\n", "    ResidualL<PERSON>er-87       [-1, 64, 15, 15, 15]               0\n", "      BatchNorm3d-88       [-1, 64, 15, 15, 15]             128\n", "             ReLU-89       [-1, 64, 15, 15, 15]               0\n", "           Conv3d-90       [-1, 64, 15, 15, 15]           4,160\n", "      BatchNorm3d-91       [-1, 64, 15, 15, 15]             128\n", "             ReLU-92       [-1, 64, 15, 15, 15]               0\n", "           Conv3d-93        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-94        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-95        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-96        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-97        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-98        [-1, 4, 15, 15, 15]           6,916\n", "           Conv3d-99        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-100        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-101        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-102        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-103        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-104        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-105        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-106        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-107        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-108        [-1, 4, 15, 15, 15]           6,916\n", "     BatchNorm3d-109       [-1, 64, 15, 15, 15]             128\n", "            ReLU-110       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-111       [-1, 64, 15, 15, 15]           4,160\n", "     BatchNorm3d-112       [-1, 64, 15, 15, 15]             128\n", "            ReLU-113       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-114       [-1, 64, 15, 15, 15]           4,160\n", "   Residual<PERSON><PERSON><PERSON>-115       [-1, 64, 15, 15, 15]               0\n", "     BatchNorm3d-116       [-1, 64, 15, 15, 15]             128\n", "            ReLU-117       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-118       [-1, 64, 15, 15, 15]           4,160\n", "     BatchNorm3d-119       [-1, 64, 15, 15, 15]             128\n", "            ReLU-120       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-121        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-122        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-123        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-124        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-125        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-126        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-127        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-128        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-129        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-130        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-131        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-132        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-133        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-134        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-135        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-136        [-1, 4, 15, 15, 15]           6,916\n", "     BatchNorm3d-137       [-1, 64, 15, 15, 15]             128\n", "            ReLU-138       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-139       [-1, 64, 15, 15, 15]           4,160\n", "     BatchNorm3d-140       [-1, 64, 15, 15, 15]             128\n", "            ReLU-141       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-142       [-1, 64, 15, 15, 15]           4,160\n", "   Residual<PERSON><PERSON>er-143       [-1, 64, 15, 15, 15]               0\n", "     BatchNorm3d-144       [-1, 64, 15, 15, 15]             128\n", "            ReLU-145       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-146       [-1, 64, 15, 15, 15]           4,160\n", "     BatchNorm3d-147       [-1, 64, 15, 15, 15]             128\n", "            ReLU-148       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-149        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-150        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-151        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-152        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-153        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-154        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-155        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-156        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-157        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-158        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-159        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-160        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-161        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-162        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-163        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-164        [-1, 4, 15, 15, 15]           6,916\n", "     BatchNorm3d-165       [-1, 64, 15, 15, 15]             128\n", "            ReLU-166       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-167       [-1, 64, 15, 15, 15]           4,160\n", "     BatchNorm3d-168       [-1, 64, 15, 15, 15]             128\n", "            ReLU-169       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-170       [-1, 64, 15, 15, 15]           4,160\n", "   Residual<PERSON><PERSON>er-171       [-1, 64, 15, 15, 15]               0\n", "     BatchNorm3d-172       [-1, 64, 15, 15, 15]             128\n", "            ReLU-173       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-174       [-1, 64, 15, 15, 15]           4,160\n", "     BatchNorm3d-175       [-1, 64, 15, 15, 15]             128\n", "            ReLU-176       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-177        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-178        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-179        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-180        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-181        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-182        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-183        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-184        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-185        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-186        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-187        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-188        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-189        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-190        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-191        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-192        [-1, 4, 15, 15, 15]           6,916\n", "     BatchNorm3d-193       [-1, 64, 15, 15, 15]             128\n", "            ReLU-194       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-195       [-1, 64, 15, 15, 15]           4,160\n", "     BatchNorm3d-196       [-1, 64, 15, 15, 15]             128\n", "            ReLU-197       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-198       [-1, 64, 15, 15, 15]           4,160\n", "   Residual<PERSON><PERSON><PERSON>-199       [-1, 64, 15, 15, 15]               0\n", "     BatchNorm3d-200       [-1, 64, 15, 15, 15]             128\n", "            ReLU-201       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-202       [-1, 64, 15, 15, 15]           4,160\n", "     BatchNorm3d-203       [-1, 64, 15, 15, 15]             128\n", "            ReLU-204       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-205        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-206        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-207        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-208        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-209        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-210        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-211        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-212        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-213        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-214        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-215        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-216        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-217        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-218        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-219        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-220        [-1, 4, 15, 15, 15]           6,916\n", "     BatchNorm3d-221       [-1, 64, 15, 15, 15]             128\n", "            ReLU-222       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-223       [-1, 64, 15, 15, 15]           4,160\n", "     BatchNorm3d-224       [-1, 64, 15, 15, 15]             128\n", "            ReLU-225       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-226       [-1, 64, 15, 15, 15]           4,160\n", "   Residual<PERSON><PERSON>er-227       [-1, 64, 15, 15, 15]               0\n", "     BatchNorm3d-228       [-1, 64, 15, 15, 15]             128\n", "            ReLU-229       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-230       [-1, 64, 15, 15, 15]           4,160\n", "     BatchNorm3d-231       [-1, 64, 15, 15, 15]             128\n", "            ReLU-232       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-233        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-234        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-235        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-236        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-237        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-238        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-239        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-240        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-241        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-242        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-243        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-244        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-245        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-246        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-247        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-248        [-1, 4, 15, 15, 15]           6,916\n", "     BatchNorm3d-249       [-1, 64, 15, 15, 15]             128\n", "            ReLU-250       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-251       [-1, 64, 15, 15, 15]           4,160\n", "     BatchNorm3d-252       [-1, 64, 15, 15, 15]             128\n", "            ReLU-253       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-254       [-1, 64, 15, 15, 15]           4,160\n", "   Resi<PERSON><PERSON><PERSON><PERSON><PERSON>-255       [-1, 64, 15, 15, 15]               0\n", "     BatchNorm3d-256       [-1, 64, 15, 15, 15]             128\n", "            ReLU-257       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-258       [-1, 64, 15, 15, 15]           4,160\n", "     BatchNorm3d-259       [-1, 64, 15, 15, 15]             128\n", "            ReLU-260       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-261        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-262        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-263        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-264        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-265        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-266        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-267        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-268        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-269        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-270        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-271        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-272        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-273        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-274        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-275        [-1, 4, 15, 15, 15]           6,916\n", "          Conv3d-276        [-1, 4, 15, 15, 15]           6,916\n", "     BatchNorm3d-277       [-1, 64, 15, 15, 15]             128\n", "            ReLU-278       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-279       [-1, 64, 15, 15, 15]           4,160\n", "     BatchNorm3d-280       [-1, 64, 15, 15, 15]             128\n", "            ReLU-281       [-1, 64, 15, 15, 15]               0\n", "          Conv3d-282       [-1, 64, 15, 15, 15]           4,160\n", "   Residual<PERSON><PERSON>er-283       [-1, 64, 15, 15, 15]               0\n", "       MaxPool3d-284          [-1, 64, 7, 7, 7]               0\n", "     BatchNorm3d-285          [-1, 64, 7, 7, 7]             128\n", "            ReLU-286          [-1, 64, 7, 7, 7]               0\n", "          Conv3d-287          [-1, 64, 7, 7, 7]           4,160\n", "     BatchNorm3d-288          [-1, 64, 7, 7, 7]             128\n", "            ReLU-289          [-1, 64, 7, 7, 7]               0\n", "          Conv3d-290           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-291           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-292           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-293           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-294           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-295           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-296           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-297           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-298           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-299           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-300           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-301           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-302           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-303           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-304           [-1, 4, 7, 7, 7]           6,916\n", "          Conv3d-305           [-1, 4, 7, 7, 7]           6,916\n", "     BatchNorm3d-306          [-1, 64, 7, 7, 7]             128\n", "            ReLU-307          [-1, 64, 7, 7, 7]               0\n", "          Conv3d-308         [-1, 256, 7, 7, 7]          16,640\n", "     BatchNorm3d-309          [-1, 64, 7, 7, 7]             128\n", "            ReLU-310          [-1, 64, 7, 7, 7]               0\n", "          Conv3d-311         [-1, 256, 7, 7, 7]          16,640\n", "   ResidualLayer-312         [-1, 256, 7, 7, 7]               0\n", "     BatchNorm3d-313         [-1, 256, 7, 7, 7]             512\n", "            ReLU-314         [-1, 256, 7, 7, 7]               0\n", "          Conv3d-315         [-1, 256, 7, 7, 7]          65,792\n", "     BatchNorm3d-316         [-1, 256, 7, 7, 7]             512\n", "            ReLU-317         [-1, 256, 7, 7, 7]               0\n", "          Conv3d-318          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-319          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-320          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-321          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-322          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-323          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-324          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-325          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-326          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-327          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-328          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-329          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-330          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-331          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-332          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-333          [-1, 16, 7, 7, 7]         110,608\n", "     BatchNorm3d-334         [-1, 256, 7, 7, 7]             512\n", "            ReLU-335         [-1, 256, 7, 7, 7]               0\n", "          Conv3d-336         [-1, 256, 7, 7, 7]          65,792\n", "     BatchNorm3d-337         [-1, 256, 7, 7, 7]             512\n", "            ReLU-338         [-1, 256, 7, 7, 7]               0\n", "          Conv3d-339         [-1, 256, 7, 7, 7]          65,792\n", "   Residual<PERSON><PERSON>er-340         [-1, 256, 7, 7, 7]               0\n", "     BatchNorm3d-341         [-1, 256, 7, 7, 7]             512\n", "            ReLU-342         [-1, 256, 7, 7, 7]               0\n", "          Conv3d-343         [-1, 256, 7, 7, 7]          65,792\n", "     BatchNorm3d-344         [-1, 256, 7, 7, 7]             512\n", "            ReLU-345         [-1, 256, 7, 7, 7]               0\n", "          Conv3d-346          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-347          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-348          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-349          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-350          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-351          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-352          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-353          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-354          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-355          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-356          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-357          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-358          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-359          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-360          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-361          [-1, 16, 7, 7, 7]         110,608\n", "     BatchNorm3d-362         [-1, 256, 7, 7, 7]             512\n", "            ReLU-363         [-1, 256, 7, 7, 7]               0\n", "          Conv3d-364         [-1, 256, 7, 7, 7]          65,792\n", "     BatchNorm3d-365         [-1, 256, 7, 7, 7]             512\n", "            ReLU-366         [-1, 256, 7, 7, 7]               0\n", "          Conv3d-367         [-1, 256, 7, 7, 7]          65,792\n", "   Residual<PERSON><PERSON><PERSON>-368         [-1, 256, 7, 7, 7]               0\n", "     BatchNorm3d-369         [-1, 256, 7, 7, 7]             512\n", "            ReLU-370         [-1, 256, 7, 7, 7]               0\n", "          Conv3d-371         [-1, 256, 7, 7, 7]          65,792\n", "     BatchNorm3d-372         [-1, 256, 7, 7, 7]             512\n", "            ReLU-373         [-1, 256, 7, 7, 7]               0\n", "          Conv3d-374          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-375          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-376          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-377          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-378          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-379          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-380          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-381          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-382          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-383          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-384          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-385          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-386          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-387          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-388          [-1, 16, 7, 7, 7]         110,608\n", "          Conv3d-389          [-1, 16, 7, 7, 7]         110,608\n", "     BatchNorm3d-390         [-1, 256, 7, 7, 7]             512\n", "            ReLU-391         [-1, 256, 7, 7, 7]               0\n", "          Conv3d-392         [-1, 256, 7, 7, 7]          65,792\n", "     BatchNorm3d-393         [-1, 256, 7, 7, 7]             512\n", "            ReLU-394         [-1, 256, 7, 7, 7]               0\n", "          Conv3d-395         [-1, 256, 7, 7, 7]          65,792\n", "   Residual<PERSON><PERSON><PERSON>-396         [-1, 256, 7, 7, 7]               0\n", "       MaxPool3d-397         [-1, 256, 3, 3, 3]               0\n", "     BatchNorm3d-398         [-1, 256, 3, 3, 3]             512\n", "            ReLU-399         [-1, 256, 3, 3, 3]               0\n", "          Conv3d-400         [-1, 256, 3, 3, 3]          65,792\n", "     BatchNorm3d-401         [-1, 256, 3, 3, 3]             512\n", "            ReLU-402         [-1, 256, 3, 3, 3]               0\n", "          Conv3d-403          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-404          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-405          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-406          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-407          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-408          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-409          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-410          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-411          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-412          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-413          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-414          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-415          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-416          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-417          [-1, 16, 3, 3, 3]         110,608\n", "          Conv3d-418          [-1, 16, 3, 3, 3]         110,608\n", "     BatchNorm3d-419         [-1, 256, 3, 3, 3]             512\n", "            ReLU-420         [-1, 256, 3, 3, 3]               0\n", "          Conv3d-421         [-1, 512, 3, 3, 3]         131,584\n", "     BatchNorm3d-422         [-1, 256, 3, 3, 3]             512\n", "            ReLU-423         [-1, 256, 3, 3, 3]               0\n", "          Conv3d-424         [-1, 512, 3, 3, 3]         131,584\n", "   ResidualLayer-425         [-1, 512, 3, 3, 3]               0\n", "         Dropout-426         [-1, 512, 3, 3, 3]               0\n", "AdaptiveAvgPool3d-427         [-1, 512, 1, 1, 1]               0\n", "         Flatten-428                  [-1, 512]               0\n", "          Linear-429                    [-1, 1]             513\n", "================================================================\n", "Total params: 9,521,985\n", "Trainable params: 9,521,985\n", "Non-trainable params: 0\n", "----------------------------------------------------------------\n", "Input size (MB): 1.65\n", "Forward/backward pass size (MB): 250.17\n", "Params size (MB): 36.32\n", "Estimated Total Size (MB): 288.14\n", "----------------------------------------------------------------\n"]}], "source": ["from torchsummary import summary\n", "# 假设输入张量的形状是 (batch_size, channels, depth, height, width)\n", "# 例如：(1, 16, 30, 30, 30)\n", "summary(net, (16, 30, 30, 30))  # 输入张量的形状"]}, {"cell_type": "code", "execution_count": null, "id": "559571e5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e575b82e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "d2l", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}